import {
    Box,
    Flex,
    HStack,
    IconButton,
    Input,
    InputGroup,
    InputLeftElement,
    Text,
    useColorMode,
} from '@chakra-ui/react';
import React from 'react';
import { FiMoon, FiSearch, FiSun } from 'react-icons/fi';

const Header: React.FC = () => {
  const { colorMode, toggleColorMode } = useColorMode();

  return (
    <Box
      as="header"
      bg="white"
      px={6}
      py={4}
      borderBottomWidth="1px"
      borderBottomColor="gray.200"
      boxShadow="sm"
      position="fixed"
      w="calc(100% - 16rem)"
      right="0"
      zIndex="10"
    >
      <Flex alignItems="center" justifyContent="space-between">
        <Text fontSize="xl" fontWeight="bold" color="gray.700">
          ProChat Admin
        </Text>

        <HStack spacing={4}>
          <InputGroup w="300px">
            <InputLeftElement pointerEvents="none">
              <FiSearch color="gray.300" />
            </InputLeftElement>
            <Input type="text" placeholder="Search..." />
          </InputGroup>

          <IconButton
            aria-label="Toggle dark mode"
            icon={colorMode === 'light' ? <FiMoon /> : <FiSun />}
            onClick={toggleColorMode}
          />
        </HStack>
      </Flex>
    </Box>
  );
};

export default Header;
