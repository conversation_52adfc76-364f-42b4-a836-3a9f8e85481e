{"name": "prochat-backend", "version": "1.0.0", "description": "ProChat Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["prochat", "social-media", "api", "nodejs", "express", "mysql"], "author": "ProChat Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}