# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# =============================================================================
# BACKEND (Spring Boot / Java)
# =============================================================================

# Compiled class files
backend/target/
backend/build/
backend/out/
backend/bin/
*.class

# Log files
backend/logs/
backend/*.log
backend/spring.log

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Maven
backend/.mvn/wrapper/maven-wrapper.jar
backend/.mvn/wrapper/maven-wrapper.properties
backend/.mvn/wrapper/MavenWrapperDownloader.java

# Gradle
backend/.gradle/
backend/gradle/wrapper/gradle-wrapper.jar

# IDE files
backend/.idea/
backend/*.iml
backend/*.ipr
backend/*.iws
backend/.vscode/
backend/.settings/
backend/.project
backend/.classpath

# =============================================================================
# ENVIRONMENT & CONFIGURATION
# =============================================================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Configuration files with sensitive data
backend/src/main/resources/application-prod.properties
backend/src/main/resources/application-dev.properties

# =============================================================================
# WEB ADMIN (React.js)
# =============================================================================

# Build output
web-admin/build/
web-admin/dist/
web-admin/.next/
web-admin/out/

# Dependencies
web-admin/node_modules/

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# VS Code
.vscode/
*.code-workspace

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.metadata/
.recommenders/
.settings/
.project
.classpath

app-example
