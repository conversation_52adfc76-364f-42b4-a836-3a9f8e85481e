export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  text: string;
  timestamp: string;
  read: boolean;
  media?: {
    type: 'image' | 'video' | 'audio' | 'document';
    url: string;
    thumbnailUrl?: string;
    name?: string;
    size?: number;
  }[];
  replyTo?: string;
}

export interface Chat {
  id: string;
  participants: string[];
  lastMessage?: Message;
  createdAt: string;
  updatedAt: string;
  type: 'individual' | 'group';
  name?: string; // For group chats
  image?: string; // For group chats
  unreadCount: number;
}

export interface ChatStats {
  totalChats: number;
  activeChats: number;
  totalMessages: number;
  messagesPerDay: number;
  mediaShared: number;
}

export interface ChatFilters {
  type?: 'individual' | 'group';
  search?: string;
  sortBy?: 'updatedAt' | 'createdAt' | 'messageCount';
  sortOrder?: 'asc' | 'desc';
  participantId?: string;
}
