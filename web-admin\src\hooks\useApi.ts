import { useState } from 'react';

export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const get = async (url: string) => {
    setLoading(true);
    setError(null);
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data based on URL
      if (url.includes('stats')) {
        return {
          users: { total: 1250, growth: 12.5 },
          chats: { messages: 8450, growth: 8.2 },
          financial: { transactions: 342, growth: 15.7 },
          security: { incidents: 3, resolved: 2 },
          ai: { accuracy: 94.2 }
        };
      }
      
      return {};
    } catch (err) {
      setError('Failed to fetch data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const post = async (url: string, data: any) => {
    setLoading(true);
    setError(null);
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    } catch (err) {
      setError('Failed to post data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { get, post, loading, error };
};
