{"name": "prochat-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build:android", "build:ios": "expo build:ios", "eject": "expo eject"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/stack": "^6.3.17", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-image-picker": "~14.3.2", "expo-camera": "~13.4.4", "expo-media-library": "~15.4.1", "expo-permissions": "~14.2.1", "react-native-vector-icons": "^10.0.0", "react-native-async-storage": "^1.19.3", "axios": "^1.5.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}