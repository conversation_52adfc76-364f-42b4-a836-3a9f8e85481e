const express = require('express');
const mysql = require('mysql2');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Database connection
const db = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'Ram$0101',
  database: 'prochat_db'
});

db.connect((err) => {
  if (err) {
    console.error('Database connection failed:', err);
    return;
  }
  console.log('Connected to MySQL database');
});

// JWT Secret
const JWT_SECRET = 'your-secret-key';

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.sendStatus(401);
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};

// Routes

// User registration
app.post('/api/register', async (req, res) => {
  try {
    const { username, email, password, full_name } = req.body;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const query = 'INSERT INTO users (username, email, password_hash, full_name) VALUES (?, ?, ?, ?)';
    db.query(query, [username, email, hashedPassword, full_name], (err, result) => {
      if (err) {
        console.error('Registration error:', err);
        return res.status(500).json({ error: 'Registration failed' });
      }
      
      res.status(201).json({ 
        message: 'User registered successfully',
        userId: result.insertId 
      });
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

// User login
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  
  const query = 'SELECT * FROM users WHERE username = ?';
  db.query(query, [username], async (err, results) => {
    if (err) {
      console.error('Login error:', err);
      return res.status(500).json({ error: 'Login failed' });
    }
    
    if (results.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    const user = results[0];
    const validPassword = await bcrypt.compare(password, user.password_hash);
    
    if (!validPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        balance: user.balance
      }
    });
  });
});

// Get all posts
app.get('/api/posts', (req, res) => {
  const query = `
    SELECT p.*, u.username, u.full_name, u.avatar_url, u.is_verified,
           COUNT(l.id) as likes_count,
           COUNT(c.id) as comments_count
    FROM posts p
    JOIN users u ON p.user_id = u.id
    LEFT JOIN likes l ON p.id = l.post_id
    LEFT JOIN comments c ON p.id = c.post_id
    GROUP BY p.id
    ORDER BY p.created_at DESC
  `;
  
  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching posts:', err);
      return res.status(500).json({ error: 'Failed to fetch posts' });
    }
    res.json(results);
  });
});

// Create new post
app.post('/api/posts', authenticateToken, (req, res) => {
  const { content, image_url } = req.body;
  const userId = req.user.userId;
  
  const query = 'INSERT INTO posts (user_id, content, image_url) VALUES (?, ?, ?)';
  db.query(query, [userId, content, image_url], (err, result) => {
    if (err) {
      console.error('Error creating post:', err);
      return res.status(500).json({ error: 'Failed to create post' });
    }
    
    res.status(201).json({
      message: 'Post created successfully',
      postId: result.insertId
    });
  });
});

// Like a post
app.post('/api/posts/:id/like', authenticateToken, (req, res) => {
  const postId = req.params.id;
  const userId = req.user.userId;
  
  // Check if already liked
  const checkQuery = 'SELECT * FROM likes WHERE user_id = ? AND post_id = ?';
  db.query(checkQuery, [userId, postId], (err, results) => {
    if (err) {
      console.error('Error checking like:', err);
      return res.status(500).json({ error: 'Failed to like post' });
    }
    
    if (results.length > 0) {
      // Unlike
      const deleteQuery = 'DELETE FROM likes WHERE user_id = ? AND post_id = ?';
      db.query(deleteQuery, [userId, postId], (err) => {
        if (err) {
          console.error('Error unliking post:', err);
          return res.status(500).json({ error: 'Failed to unlike post' });
        }
        res.json({ message: 'Post unliked' });
      });
    } else {
      // Like
      const insertQuery = 'INSERT INTO likes (user_id, post_id) VALUES (?, ?)';
      db.query(insertQuery, [userId, postId], (err) => {
        if (err) {
          console.error('Error liking post:', err);
          return res.status(500).json({ error: 'Failed to like post' });
        }
        
        // Add revenue to post creator
        const revenueQuery = `
          UPDATE users u
          JOIN posts p ON p.user_id = u.id
          SET u.balance = u.balance + 0.5
          WHERE p.id = ?
        `;
        db.query(revenueQuery, [postId], (err) => {
          if (err) console.error('Error updating revenue:', err);
        });
        
        res.json({ message: 'Post liked' });
      });
    }
  });
});

// Get user profile
app.get('/api/users/:id', (req, res) => {
  const userId = req.params.id;
  
  const query = 'SELECT id, username, full_name, email, avatar_url, is_verified, balance, created_at FROM users WHERE id = ?';
  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error('Error fetching user:', err);
      return res.status(500).json({ error: 'Failed to fetch user' });
    }
    
    if (results.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(results[0]);
  });
});

// Get user's posts
app.get('/api/users/:id/posts', (req, res) => {
  const userId = req.params.id;
  
  const query = `
    SELECT p.*, u.username, u.full_name, u.avatar_url, u.is_verified,
           COUNT(l.id) as likes_count,
           COUNT(c.id) as comments_count
    FROM posts p
    JOIN users u ON p.user_id = u.id
    LEFT JOIN likes l ON p.id = l.post_id
    LEFT JOIN comments c ON p.id = c.post_id
    WHERE p.user_id = ?
    GROUP BY p.id
    ORDER BY p.created_at DESC
  `;
  
  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error('Error fetching user posts:', err);
      return res.status(500).json({ error: 'Failed to fetch posts' });
    }
    res.json(results);
  });
});

// Send virtual gift
app.post('/api/posts/:id/gift', authenticateToken, (req, res) => {
  const postId = req.params.id;
  const { gift_type, amount } = req.body;
  const senderId = req.user.userId;
  
  // Get post creator
  const getCreatorQuery = 'SELECT user_id FROM posts WHERE id = ?';
  db.query(getCreatorQuery, [postId], (err, results) => {
    if (err || results.length === 0) {
      return res.status(500).json({ error: 'Post not found' });
    }
    
    const creatorId = results[0].user_id;
    
    // Record gift transaction
    const giftQuery = 'INSERT INTO gifts (sender_id, receiver_id, post_id, gift_type, amount) VALUES (?, ?, ?, ?, ?)';
    db.query(giftQuery, [senderId, creatorId, postId, gift_type, amount], (err) => {
      if (err) {
        console.error('Error sending gift:', err);
        return res.status(500).json({ error: 'Failed to send gift' });
      }
      
      // Update balances
      const updateBalancesQuery = `
        UPDATE users SET balance = balance - ? WHERE id = ?;
        UPDATE users SET balance = balance + ? WHERE id = ?;
      `;
      
      db.query('UPDATE users SET balance = balance - ? WHERE id = ?', [amount, senderId], (err) => {
        if (err) {
          console.error('Error updating sender balance:', err);
          return res.status(500).json({ error: 'Failed to update balance' });
        }
        
        db.query('UPDATE users SET balance = balance + ? WHERE id = ?', [amount, creatorId], (err) => {
          if (err) {
            console.error('Error updating receiver balance:', err);
            return res.status(500).json({ error: 'Failed to update balance' });
          }
          
          res.json({ message: 'Gift sent successfully' });
        });
      });
    });
  });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
