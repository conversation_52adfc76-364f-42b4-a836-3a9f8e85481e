# 🔧 ProChat Backend API

Express.js backend API for the ProChat social media platform with built-in monetization features.

## ✨ Features

### 🔐 Authentication & Authorization
- **JWT Authentication** - Secure token-based authentication
- **User Registration** - Account creation with email verification
- **Password Security** - bcrypt hashing and validation
- **Role-based Access** - User, creator, moderator, and admin roles
- **Session Management** - Token refresh and logout functionality

### 👥 User Management
- **User CRUD Operations** - Complete user lifecycle management
- **Profile Management** - User profiles with avatars and verification
- **Follow System** - User following and follower relationships
- **User Statistics** - Engagement and performance metrics
- **Account Status** - Active, suspended, and banned user states

### 📝 Content Management
- **Post Operations** - Create, read, update, delete posts
- **Media Upload** - Image and video file handling
- **Content Moderation** - Automated and manual content review
- **Engagement Tracking** - Likes, comments, shares, and views
- **Trending Algorithm** - Popular content discovery

### 💰 Monetization System
- **ProPay Wallet** - Digital wallet for users
- **Like Revenue** - Automatic earnings from post likes
- **Virtual Gifts** - Premium gift system with real value
- **Transaction Processing** - Secure payment handling
- **Revenue Sharing** - Creator and reposter profit distribution

### 💬 Messaging System
- **Direct Messages** - Private user-to-user communication
- **Group Chats** - Multi-user conversation support
- **Media Sharing** - File and image sharing in chats
- **Message Status** - Read receipts and delivery confirmation
- **Chat Moderation** - Content filtering and user reporting

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- MySQL 8.0+
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/prochat.git
cd prochat/simple-backend
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your database and other settings
```

4. **Setup database**
```bash
# Import the database schema
mysql -u root -p < ../database/setup_prochat.sql
```

5. **Start the server**
```bash
npm start
```

The API will be available at `http://localhost:3001`

### Environment Configuration

Create `.env` file:
```bash
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=Ram$0101
DB_NAME=prochat_db

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Server
PORT=3001
NODE_ENV=development

# File Upload
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

## 🏗️ Project Structure

```
simple-backend/
├── server.js            # Main server file
├── package.json         # Dependencies and scripts
├── .env                 # Environment variables
├── uploads/             # File upload directory
├── middleware/          # Express middleware
├── routes/              # API route handlers
├── models/              # Database models
├── controllers/         # Business logic
├── services/            # External services
├── utils/               # Utility functions
└── tests/               # Test files
```

## 📡 API Endpoints

### Authentication
```
POST /api/register       # User registration
POST /api/login          # User login
POST /api/refresh        # Token refresh
POST /api/logout         # User logout
POST /api/forgot-password # Password reset
```

### Users
```
GET    /api/users        # Get all users (admin)
GET    /api/users/:id    # Get user by ID
PUT    /api/users/:id    # Update user
DELETE /api/users/:id    # Delete user (admin)
GET    /api/users/:id/posts # Get user's posts
GET    /api/users/:id/followers # Get followers
GET    /api/users/:id/following # Get following
POST   /api/users/:id/follow # Follow user
DELETE /api/users/:id/follow # Unfollow user
```

### Posts
```
GET    /api/posts        # Get all posts
POST   /api/posts        # Create new post
GET    /api/posts/:id    # Get post by ID
PUT    /api/posts/:id    # Update post
DELETE /api/posts/:id    # Delete post
POST   /api/posts/:id/like # Like/unlike post
GET    /api/posts/:id/comments # Get post comments
POST   /api/posts/:id/comments # Add comment
POST   /api/posts/:id/repost # Repost content
```

### Transactions
```
GET    /api/transactions # Get user transactions
POST   /api/transactions # Create transaction
GET    /api/wallet/balance # Get wallet balance
POST   /api/wallet/withdraw # Withdraw funds
POST   /api/gifts        # Send virtual gift
GET    /api/gifts        # Get gift history
```

### Chats
```
GET    /api/chats        # Get user chats
POST   /api/chats        # Create new chat
GET    /api/chats/:id    # Get chat details
GET    /api/chats/:id/messages # Get chat messages
POST   /api/chats/:id/messages # Send message
PUT    /api/chats/:id/read # Mark as read
```

### Admin
```
GET    /api/admin/stats  # Platform statistics
GET    /api/admin/users  # User management
GET    /api/admin/posts  # Content moderation
GET    /api/admin/transactions # Financial overview
PUT    /api/admin/settings # Update settings
```

## 💾 Database Schema

### Core Tables
- **users** - User accounts and profiles
- **posts** - User-generated content
- **likes** - Post engagement tracking
- **comments** - Post comments and replies
- **follows** - User relationship tracking
- **messages** - Chat and direct messages
- **transactions** - Financial transactions
- **gifts** - Virtual gift records

### Relationships
```sql
-- User relationships
users -> posts (one-to-many)
users -> likes (one-to-many)
users -> comments (one-to-many)
users -> follows (many-to-many)
users -> messages (one-to-many)
users -> transactions (one-to-many)

-- Post relationships
posts -> likes (one-to-many)
posts -> comments (one-to-many)
posts -> gifts (one-to-many)
```

## 🔐 Security Features

### Authentication Security
```javascript
// JWT token generation
const generateToken = (user) => {
  return jwt.sign(
    { userId: user.id, username: user.username },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

// Password hashing
const hashPassword = async (password) => {
  return await bcrypt.hash(password, 10);
};
```

### Input Validation
```javascript
// Request validation middleware
const validatePost = (req, res, next) => {
  const { content } = req.body;
  
  if (!content || content.trim().length === 0) {
    return res.status(400).json({ error: 'Content is required' });
  }
  
  if (content.length > 280) {
    return res.status(400).json({ error: 'Content too long' });
  }
  
  next();
};
```

### Rate Limiting
```javascript
// API rate limiting
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
```

## 💰 Monetization Logic

### Like Revenue System
```javascript
// Automatic revenue on likes
const handleLike = async (postId, userId) => {
  // Add like record
  await db.query('INSERT INTO likes (user_id, post_id) VALUES (?, ?)', [userId, postId]);
  
  // Add revenue to post creator
  await db.query(`
    UPDATE users u
    JOIN posts p ON p.user_id = u.id
    SET u.balance = u.balance + 0.5
    WHERE p.id = ?
  `, [postId]);
  
  // Record transaction
  await db.query(`
    INSERT INTO transactions (user_id, type, amount, description)
    SELECT p.user_id, 'like_revenue', 0.5, 'Like revenue from post'
    FROM posts p WHERE p.id = ?
  `, [postId]);
};
```

### Virtual Gifts
```javascript
// Gift processing
const sendGift = async (senderId, receiverId, postId, giftType, amount) => {
  // Validate sender balance
  const sender = await getUserById(senderId);
  if (sender.balance < amount) {
    throw new Error('Insufficient balance');
  }
  
  // Process transaction
  await db.query('BEGIN');
  
  try {
    // Deduct from sender
    await db.query('UPDATE users SET balance = balance - ? WHERE id = ?', [amount, senderId]);
    
    // Add to receiver
    await db.query('UPDATE users SET balance = balance + ? WHERE id = ?', [amount, receiverId]);
    
    // Record gift
    await db.query(
      'INSERT INTO gifts (sender_id, receiver_id, post_id, gift_type, amount) VALUES (?, ?, ?, ?, ?)',
      [senderId, receiverId, postId, giftType, amount]
    );
    
    await db.query('COMMIT');
  } catch (error) {
    await db.query('ROLLBACK');
    throw error;
  }
};
```

## 📊 Analytics & Monitoring

### Performance Metrics
```javascript
// Request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
  });
  
  next();
};
```

### Error Handling
```javascript
// Global error handler
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({ error: err.message });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  res.status(500).json({ error: 'Internal server error' });
};
```

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### API Testing
```javascript
// Example test
const request = require('supertest');
const app = require('../server');

describe('POST /api/posts', () => {
  test('should create a new post', async () => {
    const response = await request(app)
      .post('/api/posts')
      .set('Authorization', `Bearer ${token}`)
      .send({
        content: 'Test post content'
      });
    
    expect(response.status).toBe(201);
    expect(response.body.message).toBe('Post created successfully');
  });
});
```

### Load Testing
```bash
# Using Artillery
npm install -g artillery
artillery quick --count 10 --num 100 http://localhost:3001/api/posts
```

## 📦 Deployment

### Production Setup
```bash
# Install PM2 for process management
npm install -g pm2

# Start application
pm2 start server.js --name "prochat-api"

# Setup auto-restart
pm2 startup
pm2 save
```

### Docker Deployment
```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3001

CMD ["npm", "start"]
```

### Environment Variables
```bash
# Production environment
NODE_ENV=production
PORT=3001
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
JWT_SECRET=your-production-secret
```

## 🔧 Configuration

### Database Connection
```javascript
// MySQL connection pool
const mysql = require('mysql2');

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});
```

### File Upload Configuration
```javascript
// Multer configuration
const multer = require('multer');
const path = require('path');

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});
```

## 🚀 Performance Optimization

### Caching Strategy
```javascript
// Redis caching
const redis = require('redis');
const client = redis.createClient();

const cacheMiddleware = (duration) => {
  return async (req, res, next) => {
    const key = req.originalUrl;
    const cached = await client.get(key);
    
    if (cached) {
      return res.json(JSON.parse(cached));
    }
    
    res.sendResponse = res.json;
    res.json = (body) => {
      client.setex(key, duration, JSON.stringify(body));
      res.sendResponse(body);
    };
    
    next();
  };
};
```

### Database Optimization
```sql
-- Index optimization
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_created_at ON posts(created_at);
CREATE INDEX idx_likes_post_id ON likes(post_id);
CREATE INDEX idx_likes_user_id ON likes(user_id);
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new features
4. Ensure all tests pass
5. Submit a pull request

### Development Guidelines
- Follow RESTful API conventions
- Write comprehensive tests
- Use proper error handling
- Document new endpoints
- Follow security best practices

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 📖 API Docs: [docs.prochat.com/api](https://docs.prochat.com/api)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/prochat/issues)

---

**Built with Express.js, MySQL, and Node.js**
