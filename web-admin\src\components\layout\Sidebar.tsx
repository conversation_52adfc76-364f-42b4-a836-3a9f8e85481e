import { Box, Divider, Flex, Icon, Text, VStack } from '@chakra-ui/react';
import React from 'react';
import {
    FiBarChart2,
    FiBell,
    FiDollarSign,
    FiFileText,
    FiHome,
    FiLogOut,
    FiMessageSquare,
    FiSettings,
    FiShoppingBag,
    FiUsers
} from 'react-icons/fi';
import { NavLink, useLocation } from 'react-router-dom';
interface NavItemProps {
  icon: any;
  children: React.ReactNode;
  to: string;
}

const NavItem: React.FC<NavItemProps> = ({ icon, children, to }) => {
  const location = useLocation();
  const isActive = location.pathname === to;

  return (
    <NavLink to={to} style={{ textDecoration: 'none' }}>
      <Flex
        align="center"
        p="4"
        mx="4"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        bg={isActive ? 'teal.400' : 'transparent'}
        color={isActive ? 'white' : 'gray.600'}
        _hover={{
          bg: 'teal.400',
          color: 'white',
        }}
      >
        <Icon
          mr="4"
          fontSize="16"
          as={icon}
        />
        <Text fontSize="md" fontWeight={isActive ? 'bold' : 'medium'}>
          {children}
        </Text>
      </Flex>
    </NavLink>
  );
};

const Sidebar: React.FC = () => {
  const handleLogout = () => {
    console.log('Logout clicked');
  };

  return (
    <Box
      as="nav"
      pos="fixed"
      top="0"
      left="0"
      h="100vh"
      w="64"
      bg="white"
      borderRight="1px"
      borderRightColor="gray.200"
      boxShadow="sm"
    >
      <Flex h="20" alignItems="center" mx="8" justifyContent="space-between">
        <Text fontSize="2xl" fontWeight="bold" color="teal.500">
          ProChat Admin
        </Text>
      </Flex>
      <VStack spacing={4} align="stretch" mt={6}>
        <NavItem icon={FiHome} to="/dashboard">
          Dashboard
        </NavItem>
        <NavItem icon={FiUsers} to="/users">
          Users
        </NavItem>
        <NavItem icon={FiMessageSquare} to="/chats">
          Chats
        </NavItem>
        <NavItem icon={FiDollarSign} to="/transactions">
          Transactions
        </NavItem>
        <NavItem icon={FiShoppingBag} to="/agents">
          Agents & Merchants
        </NavItem>
        <NavItem icon={FiBell} to="/notifications">
          Notifications
        </NavItem>
        <NavItem icon={FiFileText} to="/reports">
          Reports
        </NavItem>
        <NavItem icon={FiBarChart2} to="/analytics">
          Analytics
        </NavItem>
        <NavItem icon={FiSettings} to="/settings">
          Settings
        </NavItem>

        <Divider my={6} />

        <Box px={8} mt={4}>
          <Flex
            align="center"
            p="4"
            borderRadius="lg"
            role="group"
            cursor="pointer"
            _hover={{
              bg: 'red.400',
              color: 'white',
            }}
            onClick={handleLogout}
          >
            <Icon
              mr="4"
              fontSize="16"
              as={FiLogOut}
            />
            <Text fontSize="md">Logout</Text>
          </Flex>
        </Box>
      </VStack>
    </Box>
  );
};

export default Sidebar;


