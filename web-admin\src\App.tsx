import { <PERSON><PERSON><PERSON>rovider } from '@chakra-ui/react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Analytics from './pages/Analytics';
import Chats from './pages/Chats';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Settings from './pages/Settings';
import Transactions from './pages/Transactions';
import Users from './pages/Users';

function App() {
  return (
    <ChakraProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={
            <Layout title="Dashboard">
              <Dashboard />
            </Layout>
          } />
          <Route path="/users" element={
            <Layout title="Users">
              <Users />
            </Layout>
          } />
          <Route path="/chats" element={
            <Layout title="Chats">
              <Chats />
            </Layout>
          } />
          <Route path="/transactions" element={
            <Layout title="Transactions">
              <Transactions />
            </Layout>
          } />
          <Route path="/analytics" element={
            <Layout title="Analytics">
              <Analytics />
            </Layout>
          } />
          <Route path="/settings" element={
            <Layout title="Settings">
              <Settings />
            </Layout>
          } />
        </Routes>
      </Router>
    </ChakraProvider>
  );
}

export default App;
