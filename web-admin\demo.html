<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProChat Admin Panel - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar { width: 16rem; }
        .main-content { margin-left: 16rem; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar fixed top-0 left-0 h-full bg-white shadow-lg border-r border-gray-200 z-10">
        <div class="p-6">
            <h1 class="text-2xl font-bold text-teal-600">ProChat Admin</h1>
        </div>

        <nav class="mt-6">
            <a href="#dashboard" onclick="showPage('dashboard')" class="nav-link flex items-center px-6 py-3 text-gray-700 bg-teal-50 border-r-4 border-teal-500">
                <i class="fas fa-home mr-3"></i>
                Dashboard
            </a>
            <a href="#users" onclick="showPage('users')" class="nav-link flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50">
                <i class="fas fa-users mr-3"></i>
                Users
            </a>
            <a href="#chats" onclick="showPage('chats')" class="nav-link flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50">
                <i class="fas fa-comments mr-3"></i>
                Chats
            </a>
            <a href="#transactions" onclick="showPage('transactions')" class="nav-link flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50">
                <i class="fas fa-dollar-sign mr-3"></i>
                Transactions
            </a>
            <a href="#analytics" onclick="showPage('analytics')" class="nav-link flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50">
                <i class="fas fa-chart-bar mr-3"></i>
                Analytics
            </a>
            <a href="#settings" onclick="showPage('settings')" class="nav-link flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50">
                <i class="fas fa-cog mr-3"></i>
                Settings
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800">Dashboard</h2>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" placeholder="Search..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <button class="p-2 text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bell"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-6">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">ProChat Admin Dashboard</h3>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Users</p>
                            <p class="text-2xl font-bold text-gray-800">1,250</p>
                            <p class="text-sm text-green-600">↑ 12.5%</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-lg">
                            <i class="fas fa-comments text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Messages</p>
                            <p class="text-2xl font-bold text-gray-800">8,450</p>
                            <p class="text-sm text-green-600">↑ 8.2%</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-lg">
                            <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Transactions</p>
                            <p class="text-2xl font-bold text-gray-800">342</p>
                            <p class="text-sm text-green-600">↑ 15.7%</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 bg-red-100 rounded-lg">
                            <i class="fas fa-shield-alt text-red-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Security</p>
                            <p class="text-2xl font-bold text-gray-800">2/3</p>
                            <p class="text-sm text-yellow-600">→ 66.7%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">User Activity</h4>
                    <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                        <p class="text-gray-500">Chart: User Activity Over Time</p>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Financial Overview</h4>
                    <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                        <p class="text-gray-500">Chart: Transaction Volume</p>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-800">Recent Activity</h4>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                <span class="text-gray-700">New user registered: John Doe</span>
                            </div>
                            <span class="text-sm text-gray-500">2 minutes ago</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                <span class="text-gray-700">Transaction completed: TZS 50,000</span>
                            </div>
                            <span class="text-sm text-gray-500">5 minutes ago</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                                <span class="text-gray-700">Security alert resolved</span>
                            </div>
                            <span class="text-sm text-gray-500">10 minutes ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showPage(page) {
            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('bg-teal-50', 'border-r-4', 'border-teal-500', 'text-gray-700');
                link.classList.add('text-gray-600');
            });

            event.target.closest('.nav-link').classList.add('bg-teal-50', 'border-r-4', 'border-teal-500', 'text-gray-700');
            event.target.closest('.nav-link').classList.remove('text-gray-600');

            // Update page title
            const pageTitle = document.querySelector('header h2');
            pageTitle.textContent = page.charAt(0).toUpperCase() + page.slice(1);

            // Show success message
            const messages = {
                'dashboard': 'Dashboard loaded successfully!',
                'users': 'User management page loaded!',
                'chats': 'Chat management page loaded!',
                'transactions': 'Transaction management page loaded!',
                'analytics': 'Analytics dashboard loaded!',
                'settings': 'Settings page loaded!'
            };

            // Simple notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            notification.textContent = messages[page] || 'Page loaded!';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
