import {
    Box,
    Card,
    CardBody,
    CardHeader,
    Heading,
    HStack,
    SimpleGrid,
    Tab,
    TabList,
    TabPanel,
    TabPanels,
    Tabs,
    Text,
    VStack
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import {
    FiCpu,
    FiDollarSign,
    FiMessageSquare,
    FiShield,
    FiUsers
} from 'react-icons/fi';
import StatCard from '../components/StatCard';

const Dashboard = () => {
  // const { get } = useApi(); // Removed to fix unused variable warning
  const [stats, setStats] = useState({
    users: { total: 0, active: 0, new: 0, growth: 0 },
    chats: { total: 0, active: 0, messages: 0, growth: 0 },
    financial: { transactions: 0, volume: 0, revenue: 0, growth: 0 },
    security: { incidents: 0, blocked: 0, flagged: 0, resolved: 0 },
    ai: { processed: 0, moderated: 0, assisted: 0, accuracy: 0 }
  });
  // const [loading, setLoading] = useState(true); // Removed to fix unused variable warning

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Mock data for now - will be replaced with real API calls
        const mockData = {
          users: { total: 2100, active: 1750, new: 350, growth: 23.36 },
          chats: { total: 2450, active: 1890, messages: 4300, growth: 15 },
          financial: { transactions: 200, volume: 67000, revenue: 67000, growth: 21.8 },
          security: { incidents: 23, blocked: 5, flagged: 23, resolved: 18 },
          ai: { processed: 8450, moderated: 125, assisted: 340, accuracy: 95.2 }
        };

        setStats(mockData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
    };

    fetchData();
  }, []);

  // const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']; // Removed to fix unused variable warning

  return (
    <Box p={5}>
      <Heading mb={5}>Dashboard</Heading>

      {/* Main Stats */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 5 }} spacing={5} mb={8}>
        <StatCard
          title="Watumiaji"
          value={stats.users.total}
          change={stats.users.growth}
          icon={FiUsers}
          color="blue.500"
        />
        <StatCard
          title="Ujumbe"
          value={stats.chats.messages}
          change={stats.chats.growth}
          icon={FiMessageSquare}
          color="green.500"
        />
        <StatCard
          title="Miamala"
          value={stats.financial.transactions}
          change={stats.financial.growth}
          icon={FiDollarSign}
          color="purple.500"
        />
        <StatCard
          title="Usalama"
          value={`${stats.security.resolved}/${stats.security.incidents}`}
          change={stats.security.incidents > 0 ? (stats.security.resolved / stats.security.incidents) * 100 : 0}
          icon={FiShield}
          color="red.500"
        />
        <StatCard
          title="AI Usahihi"
          value={`${stats.ai.accuracy}%`}
          change={0}
          icon={FiCpu}
          color="orange.500"
          hideChange
        />
      </SimpleGrid>

      {/* Tabs for different sections */}
      <Tabs colorScheme="teal" variant="enclosed" isLazy>
        <TabList>
          <Tab>Muhtasari</Tab>
          <Tab>Watumiaji</Tab>
          <Tab>Kifedha</Tab>
          <Tab>Usalama</Tab>
          <Tab>AI Analytics</Tab>
        </TabList>

        <TabPanels>
          {/* Overview Tab */}
          <TabPanel>
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8}>
              <Card>
                <CardHeader>
                  <Heading size="md">Shughuli za Watumiaji</Heading>
                </CardHeader>
                <CardBody>
                  <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                    <VStack spacing={2}>
                      <Text fontSize="lg" fontWeight="bold" color="gray.600">User Activity Chart</Text>
                      <Text fontSize="sm" color="gray.500">Line chart showing active vs new users</Text>
                      <HStack spacing={4}>
                        <HStack>
                          <Box w="3" h="3" borderRadius="full" bg="#0088FE" />
                          <Text fontSize="xs">Active Users</Text>
                        </HStack>
                        <HStack>
                          <Box w="3" h="3" borderRadius="full" bg="#00C49F" />
                          <Text fontSize="xs">New Users</Text>
                        </HStack>
                      </HStack>
                    </VStack>
                  </Box>
                </CardBody>
              </Card>

              <Card>
                <CardHeader>
                  <Heading size="md">Miamala ya Kifedha</Heading>
                </CardHeader>
                <CardBody>
                  <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                    <VStack spacing={2}>
                      <Text fontSize="lg" fontWeight="bold" color="gray.600">Financial Overview</Text>
                      <Text fontSize="sm" color="gray.500">Monthly transaction amounts</Text>
                      <VStack spacing={1} fontSize="xs">
                        <Text>Jan: TZS 4,000</Text>
                        <Text>Feb: TZS 3,000</Text>
                        <Text>Mar: TZS 2,000</Text>
                        <Text>Apr: TZS 2,780</Text>
                      </VStack>
                    </VStack>
                  </Box>
                </CardBody>
              </Card>
            </SimpleGrid>
          </TabPanel>

          {/* Users Tab */}
          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">Watumiaji</Heading>
              </CardHeader>
              <CardBody>
                <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                  <VStack spacing={4}>
                    <Text fontSize="lg" fontWeight="bold" color="gray.600">User Distribution</Text>
                    <VStack spacing={2}>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#0088FE" />
                        <Text fontSize="sm" flex="1">Active Users</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.users.active}</Text>
                      </HStack>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#00C49F" />
                        <Text fontSize="sm" flex="1">New Users</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.users.new}</Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>
              </CardBody>
            </Card>
          </TabPanel>

          {/* Financial Tab */}
          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">Kifedha</Heading>
              </CardHeader>
              <CardBody>
                <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                  <VStack spacing={2}>
                    <Text fontSize="lg" fontWeight="bold" color="gray.600">Monthly Financial Data</Text>
                    <Text fontSize="sm" color="gray.500">Transaction amounts by month</Text>
                    <SimpleGrid columns={2} spacing={4} fontSize="xs">
                      <Text>Jan: TZS 4,000</Text>
                      <Text>Feb: TZS 3,000</Text>
                      <Text>Mar: TZS 2,000</Text>
                      <Text>Apr: TZS 2,780</Text>
                      <Text>May: TZS 1,890</Text>
                      <Text>Jun: TZS 2,390</Text>
                    </SimpleGrid>
                  </VStack>
                </Box>
              </CardBody>
            </Card>
          </TabPanel>

          {/* Security Tab */}
          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">Usalama</Heading>
              </CardHeader>
              <CardBody>
                <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                  <VStack spacing={4}>
                    <Text fontSize="lg" fontWeight="bold" color="gray.600">Security Overview</Text>
                    <VStack spacing={2}>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#0088FE" />
                        <Text fontSize="sm" flex="1">Resolved</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.security.resolved}</Text>
                      </HStack>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#FF8042" />
                        <Text fontSize="sm" flex="1">Incidents</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.security.incidents}</Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>
              </CardBody>
            </Card>
          </TabPanel>

          {/* AI Analytics Tab */}
          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">AI Analytics</Heading>
              </CardHeader>
              <CardBody>
                <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                  <VStack spacing={4}>
                    <Text fontSize="lg" fontWeight="bold" color="gray.600">AI Analytics</Text>
                    <VStack spacing={2}>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#0088FE" />
                        <Text fontSize="sm" flex="1">Processed</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.ai.processed}</Text>
                      </HStack>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#00C49F" />
                        <Text fontSize="sm" flex="1">Moderated</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.ai.moderated}</Text>
                      </HStack>
                      <HStack spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg="#FFBB28" />
                        <Text fontSize="sm" flex="1">Assisted</Text>
                        <Text fontSize="sm" fontWeight="bold">{stats.ai.assisted}</Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </Box>
              </CardBody>
            </Card>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default Dashboard;



