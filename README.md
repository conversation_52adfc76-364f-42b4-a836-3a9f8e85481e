# 🚀 ProChat - Social Media Platform with Built-in Monetization

ProChat is a comprehensive social media platform that combines the engaging features of modern social networks with innovative monetization capabilities. Users can earn real money through likes, comments, and virtual gifts while enjoying a seamless social experience.

## ✨ Features

### 📱 Mobile App (React Native)
- **Social Media Interface** - Twitter-like experience with posts, likes, comments, and reposts
- **ProPay Digital Wallet** - Built-in wallet system for earning and spending
- **Virtual Gifts** - Send and receive virtual gifts (🌹 Flower, 🪙 Coin, 💎 Diamond, 👑 Crown)
- **Monetized Engagement** - Earn 0.5 TZS per like received
- **Real-time Chat** - Direct messaging between users
- **Profile Management** - Complete user profiles with verification badges
- **Revenue Sharing** - 60/40 split between original creators and reposters

### 🌐 Web Admin Dashboard (React.js + Chakra UI)
- **User Management** - Complete CRUD operations for user accounts
- **Content Moderation** - Monitor and manage posts, comments, and media
- **Transaction Tracking** - Real-time financial transaction monitoring
- **Analytics Dashboard** - Comprehensive analytics and reporting
- **System Settings** - Configure app settings, gift prices, and revenue rates
- **Chat Monitoring** - Oversee user communications and support tickets

### 🔧 Backend API (Express.js)
- **RESTful API** - Complete API for all platform operations
- **Authentication** - JWT-based secure authentication system
- **Database Integration** - MySQL database with optimized queries
- **File Upload** - Media handling for posts and profile pictures
- **Real-time Features** - WebSocket support for live updates

### 💾 Database (MySQL)
- **Optimized Schema** - Efficient database design for scalability
- **Sample Data** - Pre-populated with realistic test data
- **Indexing** - Proper indexing for optimal performance
- **Relationships** - Well-defined foreign key relationships

## 🏗️ Architecture

```
ProChat/
├── prochat-mobile/          # React Native Mobile App
├── web-admin/              # React.js Admin Dashboard
├── simple-backend/         # Express.js API Server
├── database/              # MySQL Database Schema
└── docs/                  # Documentation
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- MySQL 8.0+
- Expo CLI (for mobile development)
- Git

### 1. Clone Repository
```bash
git clone https://github.com/your-username/prochat.git
cd prochat
```

### 2. Database Setup
```bash
# Start MySQL service
mysql -u root -p

# Import database schema
mysql -u root -p < database/setup_prochat.sql
```

### 3. Backend Setup
```bash
cd simple-backend
npm install
npm start
```

### 4. Web Admin Setup
```bash
cd web-admin
npm install
npm start
```

### 5. Mobile App Setup
```bash
cd prochat-mobile
npm install
expo start
```

## 📱 Mobile App Features

### Home Tab
- **Feed** - Chronological posts from followed users
- **Engagement** - Like, comment, repost, and share functionality
- **Revenue Tracking** - Real-time earnings display
- **Post Creation** - Text and image posting capabilities

### Discover Tab
- **Trending Topics** - Popular hashtags and content
- **Top Earners** - Leaderboard of highest-earning creators
- **Suggested Users** - User discovery and recommendations
- **Explore Content** - Browse posts by category

### Chats Tab
- **Direct Messages** - Private conversations
- **Group Chats** - Multi-user conversations
- **Media Sharing** - Send images, videos, and files
- **Message Status** - Read receipts and delivery confirmations

### Profile Tab
- **User Profile** - Complete profile management
- **ProPay Wallet** - Digital wallet with transaction history
- **Earnings Dashboard** - Revenue analytics and insights
- **Settings** - Account and privacy settings

## 💰 Monetization System

### Revenue Streams
1. **Like Revenue** - 0.5 TZS per like received
2. **Virtual Gifts** - Premium gifts from 5-100 TZS
3. **Content Sharing** - Revenue sharing for reposts
4. **Premium Features** - Subscription-based premium content

### Virtual Gifts
- 🌹 **Flower** - 5 TZS
- 🪙 **Coin** - 10 TZS  
- 💎 **Diamond** - 50 TZS
- 👑 **Crown** - 100 TZS

### ProPay Wallet
- **Digital Wallet** - Secure in-app currency system
- **Real Money** - Convert earnings to real currency
- **Transaction History** - Complete financial records
- **Withdrawal System** - Easy cash-out process

## 🔧 Configuration

### Environment Variables
```bash
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=Ram$0101
DB_NAME=prochat_db

# JWT
JWT_SECRET=your-secret-key

# API
API_URL=http://localhost:3001/api
```

### App Settings
- **Like Revenue Rate** - Configurable earning per like
- **Gift Prices** - Adjustable virtual gift pricing
- **Withdrawal Limits** - Minimum withdrawal amounts
- **Revenue Sharing** - Creator/reposter split ratios

## 📊 Analytics & Reporting

### User Analytics
- **User Growth** - Registration and retention metrics
- **Engagement Rates** - Likes, comments, shares analysis
- **Revenue Tracking** - Earnings and spending patterns
- **Geographic Data** - User location insights

### Content Analytics
- **Post Performance** - Engagement metrics per post
- **Trending Content** - Popular posts and hashtags
- **Creator Insights** - Top-performing content creators
- **Revenue Analytics** - Monetization effectiveness

### Financial Analytics
- **Transaction Volume** - Payment processing metrics
- **Revenue Streams** - Income source breakdown
- **User Spending** - Gift and premium feature purchases
- **Withdrawal Patterns** - Cash-out behavior analysis

## 🛡️ Security Features

### Authentication
- **JWT Tokens** - Secure session management
- **Password Hashing** - bcrypt encryption
- **Rate Limiting** - API abuse prevention
- **Input Validation** - SQL injection protection

### Privacy
- **Data Encryption** - Sensitive data protection
- **Privacy Controls** - User privacy settings
- **Content Moderation** - Automated and manual review
- **Secure Payments** - PCI-compliant transactions

## 🚀 Deployment

### Production Setup
1. **Database** - MySQL 8.0+ with proper indexing
2. **Backend** - Node.js server with PM2 process manager
3. **Frontend** - Static hosting (Netlify, Vercel)
4. **Mobile** - App Store and Google Play deployment
5. **CDN** - Media file distribution
6. **SSL** - HTTPS encryption for all endpoints

### Scaling Considerations
- **Database Sharding** - Horizontal scaling for large datasets
- **Caching** - Redis for session and data caching
- **Load Balancing** - Multiple server instances
- **CDN Integration** - Global content delivery
- **Microservices** - Service separation for scalability

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/register - User registration
POST /api/login - User login
POST /api/refresh - Token refresh
POST /api/logout - User logout
```

### User Endpoints
```
GET /api/users - Get all users
GET /api/users/:id - Get user by ID
PUT /api/users/:id - Update user
DELETE /api/users/:id - Delete user
```

### Post Endpoints
```
GET /api/posts - Get all posts
POST /api/posts - Create new post
PUT /api/posts/:id - Update post
DELETE /api/posts/:id - Delete post
POST /api/posts/:id/like - Like/unlike post
```

### Transaction Endpoints
```
GET /api/transactions - Get transactions
POST /api/transactions - Create transaction
GET /api/transactions/stats - Get transaction statistics
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 💬 Discord: [ProChat Community](https://discord.gg/prochat)
- 📖 Documentation: [docs.prochat.com](https://docs.prochat.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/prochat/issues)

## 🙏 Acknowledgments

- React Native community for mobile development tools
- Chakra UI for beautiful React components
- Express.js for robust backend framework
- MySQL for reliable database management
- Expo for streamlined mobile development

---

**Built with ❤️ by the ProChat Team**
