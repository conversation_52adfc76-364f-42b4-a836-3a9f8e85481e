import React, { useState } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Flex,
  Badge,
  Avatar,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardBody,
  SimpleGrid,
  Icon,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
} from '@chakra-ui/react';
import { FiSearch, FiFilter, FiEye, FiEdit, FiTrash2, FiUsers, FiUserPlus, FiUserCheck, FiUserX } from 'react-icons/fi';
import Layout from '../components/layout/Layout';

// Sample users data
const users = [
  {
    id: 'USR001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+255712345678',
    status: 'active',
    role: 'user',
    joinDate: '2024-01-15',
    lastActive: '2024-01-20',
    totalTransactions: 15,
    totalSpent: 125000,
    avatar: 'https://bit.ly/dan-abramov'
  },
  {
    id: 'USR002',
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+255723456789',
    status: 'inactive',
    role: 'creator',
    joinDate: '2024-01-10',
    lastActive: '2024-01-18',
    totalTransactions: 8,
    totalSpent: 75000,
    avatar: 'https://bit.ly/sage-adebayo'
  },
  {
    id: 'USR003',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    phone: '+255734567890',
    status: 'suspended',
    role: 'user',
    joinDate: '2024-01-05',
    lastActive: '2024-01-19',
    totalTransactions: 22,
    totalSpent: 200000,
    avatar: 'https://bit.ly/kent-c-dodds'
  }
];

const Users = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const [userToDelete, setUserToDelete] = useState<any>(null);
  const toast = useToast();
  const cancelRef = React.useRef<HTMLButtonElement>(null);

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesStatus && matchesRole;
  });

  const handleViewUser = (user: any) => {
    setSelectedUser(user);
    onOpen();
  };

  const handleDeleteUser = (user: any) => {
    setUserToDelete(user);
    onDeleteOpen();
  };

  const confirmDelete = () => {
    toast({
      title: 'User deleted',
      description: `${userToDelete.name} has been deleted successfully.`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
    onDeleteClose();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'yellow';
      case 'suspended':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'purple';
      case 'creator':
        return 'blue';
      case 'user':
        return 'gray';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <Layout title="Users">
      <Box p={4}>
        <Flex justifyContent="space-between" alignItems="center" mb={6}>
          <Heading size="lg">User Management</Heading>
          <Button leftIcon={<FiUserPlus />} colorScheme="teal">
            Add New User
          </Button>
        </Flex>

        {/* User Stats */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="blue.50" borderRadius="md" mr={4}>
                  <Icon as={FiUsers} color="blue.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Total Users</StatLabel>
                    <StatNumber>1,250</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      12% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="green.50" borderRadius="md" mr={4}>
                  <Icon as={FiUserCheck} color="green.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Active Users</StatLabel>
                    <StatNumber>1,180</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      8% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="yellow.50" borderRadius="md" mr={4}>
                  <Icon as={FiUserX} color="yellow.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Inactive Users</StatLabel>
                    <StatNumber>45</StatNumber>
                    <StatHelpText>
                      <StatArrow type="decrease" />
                      3% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="red.50" borderRadius="md" mr={4}>
                  <Icon as={FiTrash2} color="red.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Suspended</StatLabel>
                    <StatNumber>25</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      2% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Filters */}
        <Flex mb={6} flexWrap="wrap" gap={4}>
          <InputGroup maxW="300px">
            <InputLeftElement pointerEvents="none">
              <FiSearch color="gray.300" />
            </InputLeftElement>
            <Input 
              placeholder="Search users..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
          
          <Select 
            maxW="200px" 
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </Select>
          
          <Select 
            maxW="200px"
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
          >
            <option value="all">All Roles</option>
            <option value="admin">Admin</option>
            <option value="creator">Creator</option>
            <option value="user">User</option>
          </Select>
          
          <Button leftIcon={<FiFilter />} colorScheme="gray">
            Apply Filters
          </Button>
        </Flex>

        {/* Users Table */}
        <Box overflowX="auto">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>User</Th>
                <Th>Contact</Th>
                <Th>Status</Th>
                <Th>Role</Th>
                <Th>Join Date</Th>
                <Th>Last Active</Th>
                <Th>Transactions</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredUsers.map((user) => (
                <Tr key={user.id}>
                  <Td>
                    <Flex alignItems="center">
                      <Avatar size="sm" src={user.avatar} mr={3} />
                      <Box>
                        <Text fontWeight="medium">{user.name}</Text>
                        <Text fontSize="sm" color="gray.500">{user.id}</Text>
                      </Box>
                    </Flex>
                  </Td>
                  <Td>
                    <Box>
                      <Text fontSize="sm">{user.email}</Text>
                      <Text fontSize="sm" color="gray.500">{user.phone}</Text>
                    </Box>
                  </Td>
                  <Td>
                    <Badge colorScheme={getStatusColor(user.status)}>
                      {user.status}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge colorScheme={getRoleColor(user.role)}>
                      {user.role}
                    </Badge>
                  </Td>
                  <Td>{formatDate(user.joinDate)}</Td>
                  <Td>{formatDate(user.lastActive)}</Td>
                  <Td>
                    <Box>
                      <Text fontSize="sm">{user.totalTransactions} txns</Text>
                      <Text fontSize="sm" color="gray.500">TZS {user.totalSpent.toLocaleString()}</Text>
                    </Box>
                  </Td>
                  <Td>
                    <HStack spacing={2}>
                      <Button
                        size="sm"
                        leftIcon={<FiEye />}
                        onClick={() => handleViewUser(user)}
                      >
                        View
                      </Button>
                      <Button
                        size="sm"
                        leftIcon={<FiEdit />}
                        colorScheme="blue"
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        leftIcon={<FiTrash2 />}
                        colorScheme="red"
                        onClick={() => handleDeleteUser(user)}
                      >
                        Delete
                      </Button>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>

        {/* User Detail Modal */}
        {selectedUser && (
          <Modal isOpen={isOpen} onClose={onClose} size="lg">
            <ModalOverlay />
            <ModalContent>
              <ModalHeader>User Details</ModalHeader>
              <ModalCloseButton />
              <ModalBody>
                <VStack spacing={4} align="stretch">
                  <Flex alignItems="center">
                    <Avatar size="lg" src={selectedUser.avatar} mr={4} />
                    <Box>
                      <Text fontSize="xl" fontWeight="bold">{selectedUser.name}</Text>
                      <Text color="gray.500">{selectedUser.id}</Text>
                    </Box>
                  </Flex>
                  
                  <SimpleGrid columns={2} spacing={4}>
                    <FormControl>
                      <FormLabel>Email</FormLabel>
                      <Input value={selectedUser.email} isReadOnly />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Phone</FormLabel>
                      <Input value={selectedUser.phone} isReadOnly />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Status</FormLabel>
                      <Badge colorScheme={getStatusColor(selectedUser.status)}>
                        {selectedUser.status}
                      </Badge>
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Role</FormLabel>
                      <Badge colorScheme={getRoleColor(selectedUser.role)}>
                        {selectedUser.role}
                      </Badge>
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Join Date</FormLabel>
                      <Input value={formatDate(selectedUser.joinDate)} isReadOnly />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Last Active</FormLabel>
                      <Input value={formatDate(selectedUser.lastActive)} isReadOnly />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Total Transactions</FormLabel>
                      <Input value={selectedUser.totalTransactions} isReadOnly />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Total Spent</FormLabel>
                      <Input value={`TZS ${selectedUser.totalSpent.toLocaleString()}`} isReadOnly />
                    </FormControl>
                  </SimpleGrid>
                </VStack>
              </ModalBody>
              <ModalFooter>
                <Button colorScheme="blue" mr={3} onClick={onClose}>
                  Close
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          isOpen={isDeleteOpen}
          leastDestructiveRef={cancelRef}
          onClose={onDeleteClose}
        >
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Delete User
              </AlertDialogHeader>

              <AlertDialogBody>
                Are you sure you want to delete {userToDelete?.name}? This action cannot be undone.
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button ref={cancelRef} onClick={onDeleteClose}>
                  Cancel
                </Button>
                <Button colorScheme="red" onClick={confirmDelete} ml={3}>
                  Delete
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Box>
    </Layout>
  );
};

export default Users;
