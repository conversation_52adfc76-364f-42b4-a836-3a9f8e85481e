-- ProChat Database Setup
-- MySQL Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS prochat_db;
USE prochat_db;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    avatar_url VARCHAR(255) DEFAULT NULL,
    bio TEXT DEFAULT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    balance DECIMAL(10,2) DEFAULT 0.00,
    followers_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    posts_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Posts table
CREATE TABLE posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    image_url VARCHAR(255) DEFAULT NULL,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    reposts_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Likes table
CREATE TABLE likes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    post_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_like (user_id, post_id)
);

-- Comments table
CREATE TABLE comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    post_id INT NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
);

-- Follows table
CREATE TABLE follows (
    id INT AUTO_INCREMENT PRIMARY KEY,
    follower_id INT NOT NULL,
    following_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_follow (follower_id, following_id)
);

-- Messages table
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    content TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Gifts table
CREATE TABLE gifts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    post_id INT DEFAULT NULL,
    gift_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE SET NULL
);

-- Transactions table
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal', 'gift_sent', 'gift_received', 'like_revenue', 'comment_revenue') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Reposts table
CREATE TABLE reposts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    post_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_repost (user_id, post_id)
);

-- Notifications table
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert sample data
INSERT INTO users (username, email, password_hash, full_name, avatar_url, is_verified, balance) VALUES
('johndoe', '<EMAIL>', '$2b$10$example', 'John Doe', 'https://randomuser.me/api/portraits/men/1.jpg', TRUE, 150.00),
('janesmith', '<EMAIL>', '$2b$10$example', 'Jane Smith', 'https://randomuser.me/api/portraits/women/2.jpg', FALSE, 89.50),
('mikejohnson', '<EMAIL>', '$2b$10$example', 'Mike Johnson', 'https://randomuser.me/api/portraits/men/3.jpg', TRUE, 234.75),
('sarahwilson', '<EMAIL>', '$2b$10$example', 'Sarah Wilson', 'https://randomuser.me/api/portraits/women/4.jpg', TRUE, 312.25),
('davidbrown', '<EMAIL>', '$2b$10$example', 'David Brown', 'https://randomuser.me/api/portraits/men/5.jpg', FALSE, 67.80);

-- Insert sample posts
INSERT INTO posts (user_id, content, image_url, likes_count, comments_count, revenue) VALUES
(1, 'Just launched my new app! 🚀 Excited to share it with the world. #ProChat #ReactNative', 'https://picsum.photos/400/300?random=1', 42, 8, 21.00),
(2, 'Beautiful sunset today! 🌅 Nature never fails to amaze me. What\'s your favorite time of day?', 'https://picsum.photos/400/300?random=2', 89, 15, 44.50),
(3, 'Working on some exciting new features for ProChat! Can\'t wait to show you all what we\'ve been building. 💻✨', NULL, 67, 12, 33.50),
(4, 'Amazing coffee this morning ☕ Starting the day right! What\'s your favorite morning routine?', 'https://picsum.photos/400/300?random=4', 156, 23, 78.00),
(5, 'Just finished reading an amazing book! 📚 "The Power of Now" - highly recommend it to everyone. Life-changing insights!', NULL, 234, 67, 117.00);

-- Insert sample likes
INSERT INTO likes (user_id, post_id) VALUES
(1, 2), (1, 3), (1, 4),
(2, 1), (2, 3), (2, 5),
(3, 1), (3, 2), (3, 4), (3, 5),
(4, 1), (4, 2), (4, 3), (4, 5),
(5, 1), (5, 2), (5, 3), (5, 4);

-- Insert sample comments
INSERT INTO comments (user_id, post_id, content) VALUES
(2, 1, 'Congratulations on the launch! 🎉'),
(3, 1, 'Looking forward to trying it out!'),
(1, 2, 'Absolutely beautiful! 😍'),
(4, 2, 'I love golden hour too!'),
(5, 3, 'Can\'t wait to see the new features!'),
(1, 4, 'Coffee is life! ☕'),
(2, 5, 'Thanks for the recommendation!');

-- Insert sample follows
INSERT INTO follows (follower_id, following_id) VALUES
(1, 2), (1, 3), (1, 4),
(2, 1), (2, 3), (2, 5),
(3, 1), (3, 2), (3, 4), (3, 5),
(4, 1), (4, 2), (4, 3),
(5, 1), (5, 2), (5, 3), (5, 4);

-- Insert sample gifts
INSERT INTO gifts (sender_id, receiver_id, post_id, gift_type, amount) VALUES
(2, 1, 1, 'flower', 5.00),
(3, 2, 2, 'coin', 10.00),
(4, 3, 3, 'diamond', 50.00),
(5, 4, 4, 'crown', 100.00),
(1, 5, 5, 'flower', 5.00);

-- Insert sample transactions
INSERT INTO transactions (user_id, type, amount, description) VALUES
(1, 'gift_received', 5.00, 'Received flower gift'),
(2, 'gift_sent', -5.00, 'Sent flower gift'),
(2, 'gift_received', 10.00, 'Received coin gift'),
(3, 'gift_sent', -10.00, 'Sent coin gift'),
(3, 'gift_received', 50.00, 'Received diamond gift'),
(4, 'gift_sent', -50.00, 'Sent diamond gift'),
(4, 'gift_received', 100.00, 'Received crown gift'),
(5, 'gift_sent', -100.00, 'Sent crown gift');

-- Create indexes for better performance
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_created_at ON posts(created_at);
CREATE INDEX idx_likes_user_id ON likes(user_id);
CREATE INDEX idx_likes_post_id ON likes(post_id);
CREATE INDEX idx_comments_post_id ON comments(post_id);
CREATE INDEX idx_follows_follower_id ON follows(follower_id);
CREATE INDEX idx_follows_following_id ON follows(following_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_receiver_id ON messages(receiver_id);
CREATE INDEX idx_gifts_sender_id ON gifts(sender_id);
CREATE INDEX idx_gifts_receiver_id ON gifts(receiver_id);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
