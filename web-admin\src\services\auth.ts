import { post, get } from './api';

interface LoginCredentials {
  email: string;
  password: string;
}

interface LoginResponse {
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

interface AuthUser {
  id: string;
  name: string;
  email: string;
  role: string;
}

export const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
  const response = await post<LoginResponse>('/auth/login', credentials);
  
  // Store the token in localStorage
  localStorage.setItem('auth_token', response.token);
  
  return response;
};

export const logout = (): void => {
  localStorage.removeItem('auth_token');
  window.location.href = '/login';
};

export const getCurrentUser = async (): Promise<AuthUser> => {
  return await get<AuthUser>('/auth/me');
};

export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('auth_token');
};
