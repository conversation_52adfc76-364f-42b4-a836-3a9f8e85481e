import {
    Badge,
    Box,
    Button,
    Card,
    CardBody,
    Divider,
    Flex,
    Heading,
    HStack,
    Icon,
    Input,
    InputGroup,
    InputLeftElement,
    Modal,
    ModalBody,
    ModalCloseButton,
    ModalContent,
    ModalFooter,
    ModalHeader,
    ModalOverlay,
    Select,
    SimpleGrid,
    Stat,
    StatArrow,
    StatHelpText,
    StatLabel,
    StatNumber,
    Table,
    Tbody,
    Td,
    Text,
    Th,
    Thead,
    Tr,
    useDisclosure,
    useToast,
    VStack
} from '@chakra-ui/react';
import { useState } from 'react';
import {
    FiClock,
    FiEye,
    FiFile,
    FiFilter,
    FiFlag,
    FiImage,
    FiMessageSquare,
    FiSearch,
    FiShield,
    FiUsers,
    FiVideo
} from 'react-icons/fi';
import Layout from '../components/layout/Layout';

// Sample chats data
const chats = [
  {
    id: 'CHT001',
    participants: ['<PERSON>', '<PERSON>'],
    lastMessage: 'Hey, how are you doing?',
    lastMessageTime: '2024-01-20T10:30:00',
    messageCount: 45,
    status: 'active',
    type: 'private',
    flagged: false,
    hasMedia: true,
    createdAt: '2024-01-15T08:00:00'
  },
  {
    id: 'CHT002',
    participants: ['Mike <PERSON>', '<PERSON>', 'Tom <PERSON>'],
    lastMessage: 'Let\'s meet tomorrow at 3 PM',
    lastMessageTime: '2024-01-20T09:15:00',
    messageCount: 128,
    status: 'active',
    type: 'group',
    flagged: true,
    hasMedia: false,
    createdAt: '2024-01-10T14:30:00'
  },
  {
    id: 'CHT003',
    participants: ['Alice Cooper', 'Bob Dylan'],
    lastMessage: 'Thanks for the help!',
    lastMessageTime: '2024-01-19T16:45:00',
    messageCount: 12,
    status: 'archived',
    type: 'private',
    flagged: false,
    hasMedia: true,
    createdAt: '2024-01-18T11:20:00'
  }
];

// Sample messages for modal
const sampleMessages = [
  {
    id: 'MSG001',
    sender: 'John Doe',
    content: 'Hey, how are you doing?',
    timestamp: '2024-01-20T10:30:00',
    type: 'text'
  },
  {
    id: 'MSG002',
    sender: 'Jane Smith',
    content: 'I\'m doing great! How about you?',
    timestamp: '2024-01-20T10:32:00',
    type: 'text'
  },
  {
    id: 'MSG003',
    sender: 'John Doe',
    content: 'image.jpg',
    timestamp: '2024-01-20T10:35:00',
    type: 'image'
  }
];

const Chats = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [flaggedFilter, setFlaggedFilter] = useState('all');
  const [selectedChat, setSelectedChat] = useState<any>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const filteredChats = chats.filter(chat => {
    const matchesSearch = chat.participants.some(p =>
      p.toLowerCase().includes(searchTerm.toLowerCase())
    ) || chat.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || chat.status === statusFilter;
    const matchesType = typeFilter === 'all' || chat.type === typeFilter;
    const matchesFlagged = flaggedFilter === 'all' ||
      (flaggedFilter === 'flagged' && chat.flagged) ||
      (flaggedFilter === 'not-flagged' && !chat.flagged);

    return matchesSearch && matchesStatus && matchesType && matchesFlagged;
  });

  const handleViewChat = (chat: any) => {
    setSelectedChat(chat);
    onOpen();
  };

  const handleFlagChat = (chatId: string) => {
    toast({
      title: 'Chat flagged',
      description: `Chat ${chatId} has been flagged for review.`,
      status: 'warning',
      duration: 3000,
      isClosable: true,
    });
  };

  // const handleArchiveChat = (chatId: string) => {
  //   toast({
  //     title: 'Chat archived',
  //     description: `Chat ${chatId} has been archived.`,
  //     status: 'success',
  //     duration: 3000,
  //     isClosable: true,
  //   });
  // };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'archived':
        return 'gray';
      case 'blocked':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'private':
        return 'blue';
      case 'group':
        return 'purple';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Icon as={FiImage} color="blue.500" />;
      case 'video':
        return <Icon as={FiVideo} color="purple.500" />;
      case 'file':
        return <Icon as={FiFile} color="gray.500" />;
      default:
        return <Icon as={FiMessageSquare} color="gray.500" />;
    }
  };

  return (
    <Layout title="Chats">
      <Box p={4}>
        <Flex justifyContent="space-between" alignItems="center" mb={6}>
          <Heading size="lg">Chat Management</Heading>
          <Button leftIcon={<FiShield />} colorScheme="red">
            Moderation Tools
          </Button>
        </Flex>

        {/* Chat Stats */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="blue.50" borderRadius="md" mr={4}>
                  <Icon as={FiMessageSquare} color="blue.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Total Chats</StatLabel>
                    <StatNumber>2,450</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      15% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="green.50" borderRadius="md" mr={4}>
                  <Icon as={FiUsers} color="green.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Active Chats</StatLabel>
                    <StatNumber>1,890</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      8% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="red.50" borderRadius="md" mr={4}>
                  <Icon as={FiFlag} color="red.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Flagged Chats</StatLabel>
                    <StatNumber>23</StatNumber>
                    <StatHelpText>
                      <StatArrow type="decrease" />
                      12% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="purple.50" borderRadius="md" mr={4}>
                  <Icon as={FiClock} color="purple.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Messages Today</StatLabel>
                    <StatNumber>8,450</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      22% from yesterday
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Filters */}
        <Flex mb={6} flexWrap="wrap" gap={4}>
          <InputGroup maxW="300px">
            <InputLeftElement pointerEvents="none">
              <FiSearch color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder="Search chats..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>

          <Select
            maxW="200px"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="archived">Archived</option>
            <option value="blocked">Blocked</option>
          </Select>

          <Select
            maxW="200px"
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="private">Private</option>
            <option value="group">Group</option>
          </Select>

          <Select
            maxW="200px"
            value={flaggedFilter}
            onChange={(e) => setFlaggedFilter(e.target.value)}
          >
            <option value="all">All Chats</option>
            <option value="flagged">Flagged Only</option>
            <option value="not-flagged">Not Flagged</option>
          </Select>

          <Button leftIcon={<FiFilter />} colorScheme="gray">
            Apply Filters
          </Button>
        </Flex>

        {/* Chats Table */}
        <Box overflowX="auto">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Chat ID</Th>
                <Th>Participants</Th>
                <Th>Last Message</Th>
                <Th>Messages</Th>
                <Th>Type</Th>
                <Th>Status</Th>
                <Th>Created</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredChats.map((chat) => (
                <Tr key={chat.id}>
                  <Td>
                    <Flex alignItems="center">
                      <Text fontWeight="medium">{chat.id}</Text>
                      {chat.flagged && <Icon as={FiFlag} color="red.500" ml={2} />}
                    </Flex>
                  </Td>
                  <Td>
                    <VStack align="start" spacing={1}>
                      {chat.participants.slice(0, 2).map((participant, index) => (
                        <Text key={index} fontSize="sm">{participant}</Text>
                      ))}
                      {chat.participants.length > 2 && (
                        <Text fontSize="sm" color="gray.500">
                          +{chat.participants.length - 2} more
                        </Text>
                      )}
                    </VStack>
                  </Td>
                  <Td>
                    <Box>
                      <Text fontSize="sm" noOfLines={1}>{chat.lastMessage}</Text>
                      <Text fontSize="xs" color="gray.500">
                        {formatDate(chat.lastMessageTime)}
                      </Text>
                    </Box>
                  </Td>
                  <Td>
                    <Flex alignItems="center">
                      <Text>{chat.messageCount}</Text>
                      {chat.hasMedia && <Icon as={FiImage} color="blue.500" ml={2} />}
                    </Flex>
                  </Td>
                  <Td>
                    <Badge colorScheme={getTypeColor(chat.type)}>
                      {chat.type}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge colorScheme={getStatusColor(chat.status)}>
                      {chat.status}
                    </Badge>
                  </Td>
                  <Td>{formatDate(chat.createdAt)}</Td>
                  <Td>
                    <HStack spacing={2}>
                      <Button
                        size="sm"
                        leftIcon={<FiEye />}
                        onClick={() => handleViewChat(chat)}
                      >
                        View
                      </Button>
                      <Button
                        size="sm"
                        leftIcon={<FiFlag />}
                        colorScheme="red"
                        onClick={() => handleFlagChat(chat.id)}
                      >
                        Flag
                      </Button>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>

        {/* Chat Detail Modal */}
        {selectedChat && (
          <Modal isOpen={isOpen} onClose={onClose} size="xl">
            <ModalOverlay />
            <ModalContent>
              <ModalHeader>Chat Details - {selectedChat.id}</ModalHeader>
              <ModalCloseButton />
              <ModalBody>
                <VStack spacing={4} align="stretch">
                  <Box>
                    <Text fontWeight="bold" mb={2}>Participants:</Text>
                    <HStack spacing={2} flexWrap="wrap">
                      {selectedChat.participants.map((participant: string, index: number) => (
                        <Badge key={index} colorScheme="blue">{participant}</Badge>
                      ))}
                    </HStack>
                  </Box>

                  <Divider />

                  <Box>
                    <Text fontWeight="bold" mb={2}>Chat Information:</Text>
                    <SimpleGrid columns={2} spacing={4}>
                      <Box>
                        <Text fontSize="sm" color="gray.500">Type</Text>
                        <Badge colorScheme={getTypeColor(selectedChat.type)}>
                          {selectedChat.type}
                        </Badge>
                      </Box>
                      <Box>
                        <Text fontSize="sm" color="gray.500">Status</Text>
                        <Badge colorScheme={getStatusColor(selectedChat.status)}>
                          {selectedChat.status}
                        </Badge>
                      </Box>
                      <Box>
                        <Text fontSize="sm" color="gray.500">Messages</Text>
                        <Text>{selectedChat.messageCount}</Text>
                      </Box>
                      <Box>
                        <Text fontSize="sm" color="gray.500">Created</Text>
                        <Text fontSize="sm">{formatDate(selectedChat.createdAt)}</Text>
                      </Box>
                    </SimpleGrid>
                  </Box>

                  <Divider />

                  <Box>
                    <Text fontWeight="bold" mb={2}>Recent Messages:</Text>
                    <VStack spacing={3} align="stretch" maxH="300px" overflowY="auto">
                      {sampleMessages.map((message) => (
                        <Box key={message.id} p={3} bg="gray.50" borderRadius="md">
                          <Flex justifyContent="space-between" alignItems="start" mb={1}>
                            <Text fontWeight="medium" fontSize="sm">{message.sender}</Text>
                            <Text fontSize="xs" color="gray.500">
                              {formatDate(message.timestamp)}
                            </Text>
                          </Flex>
                          <Flex alignItems="center">
                            {getMessageTypeIcon(message.type)}
                            <Text ml={2} fontSize="sm">{message.content}</Text>
                          </Flex>
                        </Box>
                      ))}
                    </VStack>
                  </Box>

                  <Divider />

                  <Box>
                    <Text fontWeight="bold" mb={2}>Moderation Actions:</Text>
                    <HStack spacing={2}>
                      <Button size="sm" colorScheme="red" leftIcon={<FiFlag />}>
                        Flag Chat
                      </Button>
                      <Button size="sm" colorScheme="gray">
                        Archive Chat
                      </Button>
                      <Button size="sm" colorScheme="red" variant="outline">
                        Block Chat
                      </Button>
                    </HStack>
                  </Box>
                </VStack>
              </ModalBody>
              <ModalFooter>
                <Button colorScheme="blue" mr={3} onClick={onClose}>
                  Close
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>
        )}
      </Box>
    </Layout>
  );
};

export default Chats;
