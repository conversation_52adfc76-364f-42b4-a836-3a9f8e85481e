import {
    Box,
    Button,
    Card,
    CardBody,
    CardHeader,
    Divider,
    FormControl,
    FormHelperText,
    FormLabel,
    Heading,
    HStack,
    Input,
    InputGroup,
    InputRightAddon,
    SimpleGrid,
    Slider,
    SliderFilledTrack,
    SliderThumb,
    SliderTrack,
    Switch,
    Tab,
    TabList,
    TabPanel,
    TabPanels,
    Tabs,
    Text,
    useToast,
    VStack,
} from '@chakra-ui/react';
import { useState } from 'react';
import Layout from '../components/layout/Layout';

const Settings = () => {
  const toast = useToast();

  // Revenue settings
  const [vatRate, setVatRate] = useState(18);
  const [commissionRate, setCommissionRate] = useState(10);
  const [likeRate, setLikeRate] = useState(0.5);
  const [downloadRate, setDownloadRate] = useState(0.25);
  const [repostOriginalPercentage, setRepostOriginalPercentage] = useState(60);

  // Gift settings
  const [flowerValue, setFlowerValue] = useState(5);
  const [coinValue, setCoinValue] = useState(10);
  const [diamondValue, setDiamondValue] = useState(50);

  // Payment settings
  const [minWithdrawal, setMinWithdrawal] = useState(5000);
  const [maxWithdrawal, setMaxWithdrawal] = useState(1000000);
  const [withdrawalFee, setWithdrawalFee] = useState(500);

  // Notification settings
  const [notificationTitle, setNotificationTitle] = useState('');
  const [notificationBody, setNotificationBody] = useState('');
  const [notifyAllUsers, setNotifyAllUsers] = useState(true);

  const handleSaveRevenueSettings = () => {
    // Save revenue settings to backend
    toast({
      title: 'Settings saved',
      description: 'Revenue settings have been updated successfully.',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  const handleSaveGiftSettings = () => {
    // Save gift settings to backend
    toast({
      title: 'Settings saved',
      description: 'Gift settings have been updated successfully.',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  const handleSavePaymentSettings = () => {
    // Save payment settings to backend
    toast({
      title: 'Settings saved',
      description: 'Payment settings have been updated successfully.',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  const handleSendNotification = () => {
    if (!notificationTitle || !notificationBody) {
      toast({
        title: 'Error',
        description: 'Please provide both title and body for the notification.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    // Send notification to backend
    toast({
      title: 'Notification sent',
      description: `Notification has been sent to ${notifyAllUsers ? 'all users' : 'selected users'}.`,
      status: 'success',
      duration: 5000,
      isClosable: true,
    });

    // Reset form
    setNotificationTitle('');
    setNotificationBody('');
  };

  return (
    <Layout title="Settings">
      <Box p={4}>
        <Heading size="lg" mb={6}>System Settings</Heading>

        <Tabs colorScheme="teal" mb={8}>
          <TabList>
            <Tab>Revenue</Tab>
            <Tab>Gifts</Tab>
            <Tab>Payments</Tab>
            <Tab>Notifications</Tab>
          </TabList>

          <TabPanels>
            {/* Revenue Settings */}
            <TabPanel>
              <Card>
                <CardHeader>
                  <Heading size="md">Revenue Settings</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={6} align="stretch">
                    <FormControl>
                      <FormLabel>VAT Rate (%)</FormLabel>
                      <HStack>
                        <Slider
                          value={vatRate}
                          onChange={setVatRate}
                          min={0}
                          max={30}
                          step={1}
                          flex="1"
                        >
                          <SliderTrack>
                            <SliderFilledTrack />
                          </SliderTrack>
                          <SliderThumb />
                        </Slider>
                        <Text width="60px" textAlign="right">{vatRate}%</Text>
                      </HStack>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Commission Rate (%)</FormLabel>
                      <HStack>
                        <Slider
                          value={commissionRate}
                          onChange={setCommissionRate}
                          min={0}
                          max={50}
                          step={1}
                          flex="1"
                        >
                          <SliderTrack>
                            <SliderFilledTrack />
                          </SliderTrack>
                          <SliderThumb />
                        </Slider>
                        <Text width="60px" textAlign="right">{commissionRate}%</Text>
                      </HStack>
                    </FormControl>

                    <Divider />
                    <Heading size="sm">Content Creator Earnings</Heading>

                    <FormControl>
                      <FormLabel>Like Rate (TZS per like)</FormLabel>
                      <InputGroup>
                        <Input
                          type="number"
                          value={likeRate}
                          onChange={(e) => setLikeRate(parseFloat(e.target.value))}
                          step={0.1}
                        />
                        <InputRightAddon children="TZS" />
                      </InputGroup>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Download Rate (TZS per MB)</FormLabel>
                      <InputGroup>
                        <Input
                          type="number"
                          value={downloadRate}
                          onChange={(e) => setDownloadRate(parseFloat(e.target.value))}
                          step={0.05}
                        />
                        <InputRightAddon children="TZS" />
                      </InputGroup>
                    </FormControl>

                    <FormControl mt={4}>
                      <FormLabel>Repost Revenue Split</FormLabel>
                      <HStack spacing={4} align="center">
                        <Box flex="1">
                          <FormLabel fontSize="sm">Original Creator (%)</FormLabel>
                          <HStack>
                            <Slider
                              value={repostOriginalPercentage}
                              onChange={setRepostOriginalPercentage}
                              min={0}
                              max={100}
                              step={5}
                              flex="1"
                            >
                              <SliderTrack>
                                <SliderFilledTrack />
                              </SliderTrack>
                              <SliderThumb />
                            </Slider>
                            <Text width="60px" textAlign="right">{repostOriginalPercentage}%</Text>
                          </HStack>
                        </Box>

                        <Box flex="1">
                          <FormLabel fontSize="sm">Reposter (%)</FormLabel>
                          <HStack>
                            <Slider
                              value={100 - repostOriginalPercentage}
                              onChange={(val) => setRepostOriginalPercentage(100 - val)}
                              min={0}
                              max={100}
                              step={5}
                              flex="1"
                            >
                              <SliderTrack>
                                <SliderFilledTrack />
                              </SliderTrack>
                              <SliderThumb />
                            </Slider>
                            <Text width="60px" textAlign="right">{100 - repostOriginalPercentage}%</Text>
                          </HStack>
                        </Box>
                      </HStack>
                      <FormHelperText>
                        When a post is reposted, this determines how revenue from likes and downloads is split between the original creator and the person who reposted it.
                      </FormHelperText>
                    </FormControl>

                    <Button colorScheme="teal" onClick={handleSaveRevenueSettings}>
                      Save Revenue Settings
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>

            {/* Gift Settings */}
            <TabPanel>
              <Card>
                <CardHeader>
                  <Heading size="md">Gift Settings</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={6} align="stretch">
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                      <FormControl>
                        <FormLabel>Flower Value (TZS)</FormLabel>
                        <InputGroup>
                          <Input
                            type="number"
                            value={flowerValue}
                            onChange={(e) => setFlowerValue(parseInt(e.target.value))}
                          />
                          <InputRightAddon children="TZS" />
                        </InputGroup>
                      </FormControl>

                      <FormControl>
                        <FormLabel>Coin Value (TZS)</FormLabel>
                        <InputGroup>
                          <Input
                            type="number"
                            value={coinValue}
                            onChange={(e) => setCoinValue(parseInt(e.target.value))}
                          />
                          <InputRightAddon children="TZS" />
                        </InputGroup>
                      </FormControl>

                      <FormControl>
                        <FormLabel>Diamond Value (TZS)</FormLabel>
                        <InputGroup>
                          <Input
                            type="number"
                            value={diamondValue}
                            onChange={(e) => setDiamondValue(parseInt(e.target.value))}
                          />
                          <InputRightAddon children="TZS" />
                        </InputGroup>
                      </FormControl>
                    </SimpleGrid>

                    <Button colorScheme="teal" onClick={handleSaveGiftSettings}>
                      Save Gift Settings
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>

            {/* Payment Settings */}
            <TabPanel>
              <Card>
                <CardHeader>
                  <Heading size="md">Payment Settings</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={6} align="stretch">
                    <FormControl>
                      <FormLabel>Minimum Withdrawal Amount (TZS)</FormLabel>
                      <InputGroup>
                        <Input
                          type="number"
                          value={minWithdrawal}
                          onChange={(e) => setMinWithdrawal(parseInt(e.target.value))}
                        />
                        <InputRightAddon children="TZS" />
                      </InputGroup>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Maximum Withdrawal Amount (TZS)</FormLabel>
                      <InputGroup>
                        <Input
                          type="number"
                          value={maxWithdrawal}
                          onChange={(e) => setMaxWithdrawal(parseInt(e.target.value))}
                        />
                        <InputRightAddon children="TZS" />
                      </InputGroup>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Withdrawal Fee (TZS)</FormLabel>
                      <InputGroup>
                        <Input
                          type="number"
                          value={withdrawalFee}
                          onChange={(e) => setWithdrawalFee(parseInt(e.target.value))}
                        />
                        <InputRightAddon children="TZS" />
                      </InputGroup>
                    </FormControl>

                    <Button colorScheme="teal" onClick={handleSavePaymentSettings}>
                      Save Payment Settings
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>

            {/* Notification Settings */}
            <TabPanel>
              <Card>
                <CardHeader>
                  <Heading size="md">Notification Settings</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={6} align="stretch">
                    <FormControl>
                      <FormLabel>Notification Title</FormLabel>
                      <Input
                        value={notificationTitle}
                        onChange={(e) => setNotificationTitle(e.target.value)}
                      />
                    </FormControl>

                    <FormControl>
                      <FormLabel>Notification Body</FormLabel>
                      <Input
                        value={notificationBody}
                        onChange={(e) => setNotificationBody(e.target.value)}
                      />
                    </FormControl>

                    <FormControl>
                      <FormLabel>Notify All Users</FormLabel>
                      <Switch
                        isChecked={notifyAllUsers}
                        onChange={(e) => setNotifyAllUsers(e.target.checked)}
                      />
                    </FormControl>

                    <Button colorScheme="teal" onClick={handleSendNotification}>
                      Send Notification
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
    </Layout>
  );
};

export default Settings;


