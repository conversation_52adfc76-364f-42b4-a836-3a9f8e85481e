# ProChat Environment Configuration
# Copy this file to .env and update the values

# =============================================================================
# FRONTEND CONFIGURATION (React Native)
# =============================================================================

# API Configuration
EXPO_PUBLIC_API_URL=http://localhost:8080/api
EXPO_PUBLIC_WS_URL=ws://localhost:8080/ws

# App Configuration
EXPO_PUBLIC_APP_NAME=ProChat
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_APP_ENV=development

# =============================================================================
# BACKEND CONFIGURATION (Spring Boot)
# =============================================================================

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=prochat
DB_USERNAME=prochat_user
DB_PASSWORD=your_secure_password_here
DB_ROOT_PASSWORD=your_root_password_here

# Redis Configuration (Optional - for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# AWS CONFIGURATION
# =============================================================================

# AWS Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# S3 Configuration
AWS_S3_BUCKET=prochat-media-bucket
AWS_S3_REGION=us-east-1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRATION=86400000

# Encryption
ENCRYPTION_SECRET=your_encryption_secret_key_change_this_in_production

# =============================================================================
# SPRING BOOT PROFILES
# =============================================================================

# Active Spring Profile (dev, prod, docker)
SPRING_PROFILES_ACTIVE=dev

# =============================================================================
# WEB ADMIN CONFIGURATION
# =============================================================================

# Admin Panel Configuration
REACT_APP_API_URL=http://localhost:8080/api
REACT_APP_WS_URL=ws://localhost:8080/ws
REACT_APP_APP_NAME=ProChat Admin
REACT_APP_VERSION=1.0.0

# =============================================================================
# MONITORING CONFIGURATION (Optional)
# =============================================================================

# Grafana
GRAFANA_PASSWORD=admin123

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP Configuration for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# =============================================================================
# PAYMENT INTEGRATION (Optional)
# =============================================================================

# M-Pesa Configuration (Tanzania)
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_shortcode
MPESA_PASSKEY=your_passkey

# Tigo Pesa Configuration
TIGO_PESA_USERNAME=your_tigo_username
TIGO_PESA_PASSWORD=your_tigo_password
TIGO_PESA_ACCOUNT=your_tigo_account

# =============================================================================
# AI SERVICES CONFIGURATION (Optional)
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo

# Content Moderation
MODERATION_API_URL=https://api.moderationservice.com
MODERATION_API_KEY=your_moderation_api_key

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Debug Mode
DEBUG=true
LOG_LEVEL=DEBUG

# Development Tools
ENABLE_SWAGGER=true
ENABLE_ACTUATOR=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# SSL Configuration (Production only)
SSL_ENABLED=false
SSL_KEYSTORE_PATH=classpath:keystore.p12
SSL_KEYSTORE_PASSWORD=your_keystore_password
SSL_KEY_ALIAS=prochat

# Domain Configuration
DOMAIN_NAME=prochat.com
FRONTEND_URL=https://app.prochat.com
ADMIN_URL=https://admin.prochat.com

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Compose Configuration
COMPOSE_PROJECT_NAME=prochat
COMPOSE_FILE=docker-compose.yml

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=prochat-backups

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real credentials to version control
# 2. Use strong, unique passwords for all services
# 3. Rotate secrets regularly in production
# 4. Use AWS IAM roles instead of access keys when possible
# 5. Enable SSL/TLS in production
# 6. Configure proper firewall rules
# 7. Monitor and log all activities
# 8. Regular security audits and updates
