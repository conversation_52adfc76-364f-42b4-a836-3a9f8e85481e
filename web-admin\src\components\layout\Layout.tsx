import React from 'react';
import { Box, Flex, useColorModeValue } from '@chakra-ui/react';
import Header from './Header';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
}

const Layout: React.FC<LayoutProps> = ({ children, title = 'Dashboard' }) => {
  const bg = useColorModeValue('gray.50', 'gray.900');

  return (
    <Box minH="100vh" bg={bg}>
      <Flex>
        <Sidebar />
        <Box flex="1" ml="64">
          <Header />
          <Box p={6} pt="20">
            {children}
          </Box>
        </Box>
      </Flex>
    </Box>
  );
};

export default Layout;
