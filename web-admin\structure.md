web-admin/
├── public/                 # Static assets
│   ├── favicon.ico
│   └── logo.png
├── src/
│   ├── components/         # Reusable components
│   │   ├── layout/
│   │   │   ├── Sidebar.tsx
│   │   │   ├── Header.tsx
│   │   │   └── Layout.tsx
│   │   ├── dashboard/
│   │   │   ├── StatsCard.tsx
│   │   │   ├── UserActivity.tsx
│   │   │   └── RecentChats.tsx
│   │   ├── users/
│   │   │   ├── UserList.tsx
│   │   │   ├── UserForm.tsx
│   │   │   └── UserFilters.tsx
│   │   ├── chats/
│   │   │   ├── ChatList.tsx
│   │   │   ├── ChatView.tsx
│   │   │   └── MessageList.tsx
│   │   └── common/
│   │       ├── Button.tsx
│   │       ├── Table.tsx
│   │       ├── Modal.tsx
│   │       └── Card.tsx
│   ├── pages/              # Page components
│   │   ├── Dashboard.tsx
│   │   ├── Users.tsx
│   │   ├── Chats.tsx
│   │   ├── Settings.tsx
│   │   └── Login.tsx
│   ├── services/           # API services
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   ├── users.ts
│   │   └── chats.ts
│   ├── context/            # React context
│   │   ├── AuthContext.tsx
│   │   └── ThemeContext.tsx
│   ├── hooks/              # Custom hooks
│   │   ├── useAuth.ts
│   │   └── useApi.ts
│   ├── utils/              # Utility functions
│   │   ├── formatters.ts
│   │   └── validators.ts
│   ├── types/              # TypeScript types
│   │   ├── user.ts
│   │   └── chat.ts
│   ├── App.tsx             # Main App component
│   ├── index.tsx           # Entry point
│   └── routes.tsx          # Route definitions
├── package.json
├── tsconfig.json
└── README.md
