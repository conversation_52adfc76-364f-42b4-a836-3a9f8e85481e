import {
    Badge,
    Box,
    Button,
    Card,
    CardBody,
    CardHeader,
    Flex,
    Heading,
    HStack,
    Icon,
    Progress,
    Select,
    SimpleGrid,
    Stat,
    StatArrow,
    StatHelpText,
    StatLabel,
    StatNumber,
    Table,
    Tbody,
    Td,
    Text,
    Th,
    Thead,
    Tr,
    VStack
} from '@chakra-ui/react';
import { useState } from 'react';
import {
    FiActivity,
    FiDollarSign,
    FiDownload,
    FiGlobe,
    FiMessageSquare,
    FiUsers
} from 'react-icons/fi';
import Layout from '../components/layout/Layout';

// Sample data for charts
const userGrowthData = [
  { month: 'Jan', users: 1200, active: 980 },
  { month: 'Feb', users: 1350, active: 1100 },
  { month: 'Mar', users: 1500, active: 1250 },
  { month: 'Apr', users: 1680, active: 1400 },
  { month: 'May', users: 1850, active: 1550 },
  { month: 'Jun', users: 2100, active: 1750 },
];

const messageData = [
  { day: 'Mon', messages: 2400 },
  { day: '<PERSON><PERSON>', messages: 1398 },
  { day: 'Wed', messages: 9800 },
  { day: 'Thu', messages: 3908 },
  { day: 'Fri', messages: 4800 },
  { day: 'Sat', messages: 3800 },
  { day: 'Sun', messages: 4300 },
];

const revenueData = [
  { month: 'Jan', revenue: 45000, transactions: 120 },
  { month: 'Feb', revenue: 52000, transactions: 145 },
  { month: 'Mar', revenue: 48000, transactions: 135 },
  { month: 'Apr', revenue: 61000, transactions: 180 },
  { month: 'May', revenue: 55000, transactions: 165 },
  { month: 'Jun', revenue: 67000, transactions: 200 },
];

const deviceData = [
  { name: 'Mobile', value: 65, color: '#0088FE' },
  { name: 'Desktop', value: 25, color: '#00C49F' },
  { name: 'Tablet', value: 10, color: '#FFBB28' },
];

const topCountries = [
  { country: 'Tanzania', users: 1250, percentage: 45 },
  { country: 'Kenya', users: 890, percentage: 32 },
  { country: 'Uganda', users: 420, percentage: 15 },
  { country: 'Rwanda', users: 220, percentage: 8 },
];

const Analytics = () => {
  const [timeRange, setTimeRange] = useState('30d');

  // const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']; // Removed to fix unused variable warning

  return (
    <Layout title="Analytics">
      <Box p={4}>
        <Flex justifyContent="space-between" alignItems="center" mb={6}>
          <Heading size="lg">Analytics Dashboard</Heading>
          <HStack spacing={4}>
            <Select value={timeRange} onChange={(e) => setTimeRange(e.target.value)} maxW="200px">
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </Select>
            <Button leftIcon={<FiDownload />} colorScheme="teal">
              Export Report
            </Button>
          </HStack>
        </Flex>

        {/* Key Metrics */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="blue.50" borderRadius="md" mr={4}>
                  <Icon as={FiUsers} color="blue.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Total Users</StatLabel>
                    <StatNumber>2,100</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      23.36% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="green.50" borderRadius="md" mr={4}>
                  <Icon as={FiActivity} color="green.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Active Users</StatLabel>
                    <StatNumber>1,750</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      18.2% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="purple.50" borderRadius="md" mr={4}>
                  <Icon as={FiMessageSquare} color="purple.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Messages/Day</StatLabel>
                    <StatNumber>4,300</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      12.5% from yesterday
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="orange.50" borderRadius="md" mr={4}>
                  <Icon as={FiDollarSign} color="orange.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Revenue</StatLabel>
                    <StatNumber>TZS 67K</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      21.8% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Charts Section */}
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} mb={8}>
          {/* User Growth Chart */}
          <Card>
            <CardHeader>
              <Heading size="md">User Growth</Heading>
            </CardHeader>
            <CardBody>
              <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                <VStack spacing={2}>
                  <Text fontSize="lg" fontWeight="bold" color="gray.600">User Growth Chart</Text>
                  <Text fontSize="sm" color="gray.500">Chart will be displayed here</Text>
                  <Text fontSize="xs" color="gray.400">Data: {userGrowthData.length} months</Text>
                </VStack>
              </Box>
            </CardBody>
          </Card>

          {/* Message Activity Chart */}
          <Card>
            <CardHeader>
              <Heading size="md">Daily Message Activity</Heading>
            </CardHeader>
            <CardBody>
              <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                <VStack spacing={2}>
                  <Text fontSize="lg" fontWeight="bold" color="gray.600">Message Activity Chart</Text>
                  <Text fontSize="sm" color="gray.500">Bar chart will be displayed here</Text>
                  <Text fontSize="xs" color="gray.400">Data: {messageData.length} days</Text>
                </VStack>
              </Box>
            </CardBody>
          </Card>

          {/* Revenue Chart */}
          <Card>
            <CardHeader>
              <Heading size="md">Revenue & Transactions</Heading>
            </CardHeader>
            <CardBody>
              <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                <VStack spacing={2}>
                  <Text fontSize="lg" fontWeight="bold" color="gray.600">Revenue & Transactions Chart</Text>
                  <Text fontSize="sm" color="gray.500">Combined chart will be displayed here</Text>
                  <Text fontSize="xs" color="gray.400">Data: {revenueData.length} months</Text>
                </VStack>
              </Box>
            </CardBody>
          </Card>

          {/* Device Usage Chart */}
          <Card>
            <CardHeader>
              <Heading size="md">Device Usage</Heading>
            </CardHeader>
            <CardBody>
              <Box h="300px" bg="gray.100" borderRadius="lg" display="flex" alignItems="center" justifyContent="center">
                <VStack spacing={4}>
                  <Text fontSize="lg" fontWeight="bold" color="gray.600">Device Usage</Text>
                  <VStack spacing={2}>
                    {deviceData.map((device, index) => (
                      <HStack key={index} spacing={4} w="200px">
                        <Box w="4" h="4" borderRadius="full" bg={device.color} />
                        <Text fontSize="sm" flex="1">{device.name}</Text>
                        <Text fontSize="sm" fontWeight="bold">{device.value}%</Text>
                      </HStack>
                    ))}
                  </VStack>
                </VStack>
              </Box>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Additional Analytics */}
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} mb={8}>
          {/* Top Countries */}
          <Card>
            <CardHeader>
              <Heading size="md">Top Countries</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                {topCountries.map((country, index) => (
                  <Box key={index}>
                    <Flex justifyContent="space-between" alignItems="center" mb={2}>
                      <HStack>
                        <Icon as={FiGlobe} color="blue.500" />
                        <Text fontWeight="medium">{country.country}</Text>
                      </HStack>
                      <Text fontSize="sm" color="gray.500">
                        {country.users} users ({country.percentage}%)
                      </Text>
                    </Flex>
                    <Progress value={country.percentage} colorScheme="blue" size="sm" />
                  </Box>
                ))}
              </VStack>
            </CardBody>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <Heading size="md">Performance Metrics</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <Box>
                  <Flex justifyContent="space-between" alignItems="center" mb={2}>
                    <Text fontWeight="medium">User Retention Rate</Text>
                    <Badge colorScheme="green">85%</Badge>
                  </Flex>
                  <Progress value={85} colorScheme="green" size="sm" />
                </Box>

                <Box>
                  <Flex justifyContent="space-between" alignItems="center" mb={2}>
                    <Text fontWeight="medium">Daily Active Users</Text>
                    <Badge colorScheme="blue">72%</Badge>
                  </Flex>
                  <Progress value={72} colorScheme="blue" size="sm" />
                </Box>

                <Box>
                  <Flex justifyContent="space-between" alignItems="center" mb={2}>
                    <Text fontWeight="medium">Message Delivery Rate</Text>
                    <Badge colorScheme="green">99.2%</Badge>
                  </Flex>
                  <Progress value={99.2} colorScheme="green" size="sm" />
                </Box>

                <Box>
                  <Flex justifyContent="space-between" alignItems="center" mb={2}>
                    <Text fontWeight="medium">App Crash Rate</Text>
                    <Badge colorScheme="red">0.8%</Badge>
                  </Flex>
                  <Progress value={0.8} colorScheme="red" size="sm" />
                </Box>
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Recent Activity Table */}
        <Card>
          <CardHeader>
            <Heading size="md">Recent System Activity</Heading>
          </CardHeader>
          <CardBody>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Event</Th>
                  <Th>User</Th>
                  <Th>Time</Th>
                  <Th>Status</Th>
                </Tr>
              </Thead>
              <Tbody>
                <Tr>
                  <Td>New user registration</Td>
                  <Td><EMAIL></Td>
                  <Td>2 minutes ago</Td>
                  <Td><Badge colorScheme="green">Success</Badge></Td>
                </Tr>
                <Tr>
                  <Td>Payment processed</Td>
                  <Td><EMAIL></Td>
                  <Td>5 minutes ago</Td>
                  <Td><Badge colorScheme="green">Success</Badge></Td>
                </Tr>
                <Tr>
                  <Td>Message flagged</Td>
                  <Td><EMAIL></Td>
                  <Td>8 minutes ago</Td>
                  <Td><Badge colorScheme="yellow">Review</Badge></Td>
                </Tr>
                <Tr>
                  <Td>User login</Td>
                  <Td><EMAIL></Td>
                  <Td>12 minutes ago</Td>
                  <Td><Badge colorScheme="green">Success</Badge></Td>
                </Tr>
              </Tbody>
            </Table>
          </CardBody>
        </Card>
      </Box>
    </Layout>
  );
};

export default Analytics;
