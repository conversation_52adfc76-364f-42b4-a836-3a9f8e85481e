import {
    Badge,
    Box,
    Button,
    Card,
    CardBody,
    Flex,
    FormControl,
    FormLabel,
    Heading,
    Icon,
    Input,
    InputGroup,
    InputLeftElement,
    Modal,
    ModalBody,
    ModalCloseButton,
    ModalContent,
    ModalFooter,
    ModalHeader,
    ModalOverlay,
    Select,
    SimpleGrid,
    Stat,
    StatArrow,
    StatHelpText,
    StatLabel,
    StatNumber,
    Table,
    Tbody,
    Td,
    Text,
    Th,
    Thead,
    Tr,
    useDisclosure
} from '@chakra-ui/react';
import { useState } from 'react';
import { FiArrowDown, FiArrowUp, FiCreditCard, FiDollarSign, FiDownload, FiEye, FiFilter, FiSearch } from 'react-icons/fi';
import Layout from '../components/layout/Layout';

// Sample transaction data
const transactions = [
  { id: 'TX123456', userId: 'U12345', type: 'deposit', amount: 50000, method: 'M-Pesa', status: 'completed', date: '2023-06-15T10:30:00Z' },
  { id: 'TX123457', userId: 'U12346', type: 'withdrawal', amount: 25000, method: 'Tigo Pesa', status: 'completed', date: '2023-06-15T11:45:00Z' },
  { id: 'TX123458', userId: 'U12347', type: 'transfer', amount: 15000, recipient: 'U12348', status: 'completed', date: '2023-06-15T12:15:00Z' },
  { id: 'TX123459', userId: 'U12349', type: 'payment', amount: 8000, merchant: 'Shop123', status: 'completed', date: '2023-06-15T13:20:00Z' },
  { id: 'TX123460', userId: 'U12350', type: 'earning', amount: 1200, source: 'likes', status: 'completed', date: '2023-06-15T14:10:00Z' },
  { id: 'TX123461', userId: 'U12351', type: 'earning', amount: 3500, source: 'gifts', status: 'completed', date: '2023-06-15T15:05:00Z' },
  { id: 'TX123462', userId: 'U12352', type: 'deposit', amount: 100000, method: 'Bank Transfer', status: 'pending', date: '2023-06-15T16:30:00Z' },
  { id: 'TX123463', userId: 'U12353', type: 'withdrawal', amount: 75000, method: 'Azam Pesa', status: 'failed', date: '2023-06-15T17:45:00Z' },
  { id: 'TX123464', userId: 'U12354', type: 'transfer', amount: 20000, recipient: 'U12355', status: 'completed', date: '2023-06-15T18:20:00Z' },
  { id: 'TX123465', userId: 'U12356', type: 'payment', amount: 12000, merchant: 'Shop456', status: 'completed', date: '2023-06-15T19:15:00Z' },
];

const Transactions = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [selectedTransaction, setSelectedTransaction] = useState<typeof transactions[0] | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Filter transactions based on search and filters
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch =
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.userId.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;

    // Simple date filtering (in a real app, you'd use proper date filtering)
    let matchesDate = true;
    if (dateFilter === 'today') {
      const today = new Date().toISOString().split('T')[0];
      matchesDate = transaction.date.startsWith(today);
    }

    return matchesSearch && matchesType && matchesStatus && matchesDate;
  });

  const handleViewTransaction = (transaction: any) => {
    setSelectedTransaction(transaction);
    onOpen();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'failed':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <FiArrowDown color="green" />;
      case 'withdrawal':
        return <FiArrowUp color="red" />;
      case 'transfer':
        return <FiArrowUp color="blue" />;
      case 'payment':
        return <FiCreditCard color="purple" />;
      case 'earning':
        return <FiDollarSign color="orange" />;
      default:
        return <FiDollarSign color="gray" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <Layout title="Transactions">
      <Box p={4}>
        <Flex justifyContent="space-between" alignItems="center" mb={6}>
          <Heading size="lg">Transaction Management</Heading>
          <Button leftIcon={<FiDownload />} colorScheme="teal">
            Export Data
          </Button>
        </Flex>

        {/* Transaction Stats */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="blue.50" borderRadius="md" mr={4}>
                  <Icon as={FiDollarSign} color="blue.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Total Volume</StatLabel>
                    <StatNumber>TZS 4.5M</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      18% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="green.50" borderRadius="md" mr={4}>
                  <Icon as={FiArrowDown} color="green.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Deposits</StatLabel>
                    <StatNumber>TZS 2.8M</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      12% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="red.50" borderRadius="md" mr={4}>
                  <Icon as={FiArrowUp} color="red.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Withdrawals</StatLabel>
                    <StatNumber>TZS 1.2M</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      8% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex alignItems="center">
                <Box p={2} bg="orange.50" borderRadius="md" mr={4}>
                  <Icon as={FiCreditCard} color="orange.500" boxSize={6} />
                </Box>
                <Box>
                  <Stat>
                    <StatLabel>Payments</StatLabel>
                    <StatNumber>TZS 950K</StatNumber>
                    <StatHelpText>
                      <StatArrow type="increase" />
                      15% from last month
                    </StatHelpText>
                  </Stat>
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Filters */}
        <Flex mb={6} flexWrap="wrap" gap={4}>
          <InputGroup maxW="300px">
            <InputLeftElement pointerEvents="none">
              <FiSearch color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder="Search by ID or User ID"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>

          <Select
            maxW="200px"
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="deposit">Deposits</option>
            <option value="withdrawal">Withdrawals</option>
            <option value="transfer">Transfers</option>
            <option value="payment">Payments</option>
            <option value="earning">Earnings</option>
          </Select>

          <Select
            maxW="200px"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
          </Select>

          <Select
            maxW="200px"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </Select>

          <Button leftIcon={<FiFilter />} colorScheme="gray">
            Apply Filters
          </Button>
        </Flex>

        {/* Transactions Table */}
        <Box overflowX="auto">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Transaction ID</Th>
                <Th>User ID</Th>
                <Th>Type</Th>
                <Th>Amount (TZS)</Th>
                <Th>Status</Th>
                <Th>Date & Time</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredTransactions.map((transaction) => (
                <Tr key={transaction.id}>
                  <Td>{transaction.id}</Td>
                  <Td>{transaction.userId}</Td>
                  <Td>
                    <Flex alignItems="center">
                      {getTypeIcon(transaction.type)}
                      <Text ml={2} textTransform="capitalize">{transaction.type}</Text>
                    </Flex>
                  </Td>
                  <Td>{transaction.amount.toLocaleString()}</Td>
                  <Td>
                    <Badge colorScheme={getStatusColor(transaction.status)}>
                      {transaction.status}
                    </Badge>
                  </Td>
                  <Td>{formatDate(transaction.date)}</Td>
                  <Td>
                    <Button
                      size="sm"
                      leftIcon={<FiEye />}
                      onClick={() => handleViewTransaction(transaction)}
                    >
                      View
                    </Button>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>

        {/* Transaction Detail Modal */}
        {selectedTransaction && (
          <Modal isOpen={isOpen} onClose={onClose}>
            <ModalOverlay />
            <ModalContent>
              <ModalHeader>Transaction Details</ModalHeader>
              <ModalCloseButton />
              <ModalBody>
                <FormControl mb={4}>
                  <FormLabel>Transaction ID</FormLabel>
                  <Input value={selectedTransaction.id} isReadOnly />
                </FormControl>

                <FormControl mb={4}>
                  <FormLabel>User ID</FormLabel>
                  <Input value={selectedTransaction.userId} isReadOnly />
                </FormControl>

                <FormControl mb={4}>
                  <FormLabel>Type</FormLabel>
                  <Input value={selectedTransaction.type} textTransform="capitalize" isReadOnly />
                </FormControl>

                <FormControl mb={4}>
                  <FormLabel>Amount</FormLabel>
                  <Input value={`TZS ${selectedTransaction.amount.toLocaleString()}`} isReadOnly />
                </FormControl>

                {selectedTransaction.method && (
                  <FormControl mb={4}>
                    <FormLabel>Payment Method</FormLabel>
                    <Input value={selectedTransaction.method} isReadOnly />
                  </FormControl>
                )}

                {selectedTransaction.recipient && (
                  <FormControl mb={4}>
                    <FormLabel>Recipient</FormLabel>
                    <Input value={selectedTransaction.recipient} isReadOnly />
                  </FormControl>
                )}

                {selectedTransaction.merchant && (
                  <FormControl mb={4}>
                    <FormLabel>Merchant</FormLabel>
                    <Input value={selectedTransaction.merchant} isReadOnly />
                  </FormControl>
                )}

                {selectedTransaction.source && (
                  <FormControl mb={4}>
                    <FormLabel>Source</FormLabel>
                    <Input value={selectedTransaction.source} isReadOnly />
                  </FormControl>
                )}
              </ModalBody>
              <ModalFooter>
                <Button colorScheme="blue" mr={3} onClick={onClose}>
                  Close
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>
        )}
      </Box>
    </Layout>
  );
};

export default Transactions;

