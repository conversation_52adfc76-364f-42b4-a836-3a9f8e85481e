export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  profileImage?: string;
  phoneNumber?: string;
  createdAt: string;
  lastActive?: string;
  status: 'active' | 'inactive' | 'blocked';
  role: 'user' | 'admin' | 'moderator';
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
}

export interface UserFilters {
  status?: 'active' | 'inactive' | 'blocked';
  role?: 'user' | 'admin' | 'moderator';
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'lastActive';
  sortOrder?: 'asc' | 'desc';
}
