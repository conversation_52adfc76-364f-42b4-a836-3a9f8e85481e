import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Image,
    Modal,
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function App() {
  const [currentTab, setCurrentTab] = useState('home');
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showNewPost, setShowNewPost] = useState(false);
  const [newPostText, setNewPostText] = useState('');

  const currentUser = {
    id: 1,
    name: 'ProChat User',
    username: 'prochatuser',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    verified: true,
    balance: 2500.00,
    followers: 150,
    following: 89,
    posts: 23
  };

  const mockPosts = [
    {
      id: '1',
      user: {
        name: '<PERSON>',
        username: 'joh<PERSON><PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        verified: true,
      },
      content: 'Just launched my new app! 🚀 Excited to share it with the world. #ProChat #ReactNative',
      image: 'https://picsum.photos/400/300?random=1',
      likes: 42,
      comments: 8,
      reposts: 12,
      views: 156,
      revenue: 21.00,
      createdAt: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      user: {
        name: 'Jane <PERSON>',
        username: 'janesmith',
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
        verified: false,
      },
      content: 'Beautiful sunset today! 🌅 Nature never fails to amaze me. What\'s your favorite time of day?',
      image: 'https://picsum.photos/400/300?random=2',
      likes: 89,
      comments: 15,
      reposts: 23,
      views: 234,
      revenue: 44.50,
      createdAt: '2024-01-15T08:15:00Z',
    },
    {
      id: '3',
      user: {
        name: 'Mike Johnson',
        username: 'mikej',
        avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
        verified: true,
      },
      content: 'Working on some exciting new features for ProChat! Can\'t wait to show you all what we\'ve been building. 💻✨',
      likes: 67,
      comments: 12,
      reposts: 18,
      views: 189,
      revenue: 33.50,
      createdAt: '2024-01-15T06:45:00Z',
    },
  ];

  const mockChats = [
    {
      id: '1',
      name: 'John Doe',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      lastMessage: 'Hey! How are you doing?',
      time: '2m',
      unread: 2,
    },
    {
      id: '2',
      name: 'Jane Smith',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
      lastMessage: 'Thanks for the gift! 🎁',
      time: '1h',
      unread: 0,
    },
    {
      id: '3',
      name: 'ProChat Team',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      lastMessage: 'Welcome to ProChat! 🚀',
      time: '1d',
      unread: 1,
    },
  ];

  useEffect(() => {
    setTimeout(() => {
      setPosts(mockPosts);
      setLoading(false);
    }, 1000);
  }, []);

  const handleLike = (postId) => {
    Alert.alert(
      'Post Liked! ❤️',
      '0.5 TZS deducted from your ProPay balance.\nCreator earned 0.5 TZS!',
      [{ text: 'OK' }]
    );

    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, likes: post.likes + 1, revenue: post.revenue + 0.5 }
          : post
      )
    );
  };

  const handleGift = (postId) => {
    Alert.alert(
      'Send Virtual Gift 🎁',
      'Choose a gift to send:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: '🌹 Flower (5 TZS)', onPress: () => sendGift('Flower', 5, postId) },
        { text: '🪙 Coin (10 TZS)', onPress: () => sendGift('Coin', 10, postId) },
        { text: '💎 Diamond (50 TZS)', onPress: () => sendGift('Diamond', 50, postId) },
        { text: '👑 Crown (100 TZS)', onPress: () => sendGift('Crown', 100, postId) },
      ]
    );
  };

  const sendGift = (giftName, value, postId) => {
    Alert.alert(
      `Gift Sent! 🎁`,
      `You sent a ${giftName} worth ${value} TZS!\nCreator received ${value} TZS in their ProPay wallet.`,
      [{ text: 'OK' }]
    );

    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, revenue: post.revenue + value }
          : post
      )
    );
  };

  const handleRepost = (postId) => {
    Alert.alert(
      'Post Reposted! 🔄',
      'Revenue Sharing:\n• You get 40% of future earnings\n• Original creator gets 60%\n\nThis encourages quality content sharing!',
      [{ text: 'OK' }]
    );
  };

  const createNewPost = () => {
    if (newPostText.trim()) {
      const newPost = {
        id: String(posts.length + 1),
        user: currentUser,
        content: newPostText,
        likes: 0,
        comments: 0,
        reposts: 0,
        views: 0,
        revenue: 0,
        createdAt: new Date().toISOString(),
      };

      setPosts([newPost, ...posts]);
      setNewPostText('');
      setShowNewPost(false);

      Alert.alert(
        'Post Created! 🎉',
        'Your post is now live! Start earning from likes, comments, and gifts.',
        [{ text: 'OK' }]
      );
    }
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'now';
    if (diffInHours < 24) return `${diffInHours}h`;
    return `${Math.floor(diffInHours / 24)}d`;
  };

  const renderPost = ({ item: post }) => (
    <View style={styles.postContainer}>
      <View style={styles.postHeader}>
        <Image source={{ uri: post.user.avatar }} style={styles.avatar} />
        <View style={styles.userInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.userName}>{post.user.name}</Text>
            {post.user.verified && <Text style={styles.verified}>✓</Text>}
            <Text style={styles.userHandle}>@{post.user.username}</Text>
            <Text style={styles.dot}>·</Text>
            <Text style={styles.timeAgo}>{formatTimeAgo(post.createdAt)}</Text>
          </View>
        </View>
      </View>

      <Text style={styles.postContent}>{post.content}</Text>

      {post.image && (
        <Image source={{ uri: post.image }} style={styles.postImage} />
      )}

      <View style={styles.statsRow}>
        <Text style={styles.statsText}>{post.comments} Comments</Text>
        <Text style={styles.statsText}>{post.reposts} Reposts</Text>
        <Text style={styles.statsText}>{post.likes} Likes</Text>
        <Text style={styles.revenueText}>💰 {post.revenue} TZS</Text>
      </View>

      <View style={styles.actionsRow}>
        <TouchableOpacity style={styles.actionButton} onPress={() => Alert.alert('Comments', 'Comment feature coming soon!')}>
          <Text style={styles.actionText}>💬 {post.comments}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => handleRepost(post.id)}>
          <Text style={styles.actionText}>🔄 {post.reposts}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => handleLike(post.id)}>
          <Text style={styles.actionText}>❤️ {post.likes}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => handleGift(post.id)}>
          <Text style={styles.actionText}>🎁</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderHomeTab = () => (
    <FlatList
      data={posts}
      renderItem={renderPost}
      keyExtractor={(item) => item.id}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContent}
    />
  );

  const renderDiscoverTab = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionTitle}>🔥 Trending Topics</Text>
      <View style={styles.trendingContainer}>
        <TouchableOpacity style={styles.trendingItem}>
          <Text style={styles.trendingText}>#ProChat</Text>
          <Text style={styles.trendingCount}>1.2K posts</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.trendingItem}>
          <Text style={styles.trendingText}>#ReactNative</Text>
          <Text style={styles.trendingCount}>856 posts</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.trendingItem}>
          <Text style={styles.trendingText}>#ProPay</Text>
          <Text style={styles.trendingCount}>634 posts</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.sectionTitle}>💎 Top Earners</Text>
      <View style={styles.earnersContainer}>
        <View style={styles.earnerItem}>
          <Image source={{ uri: 'https://randomuser.me/api/portraits/men/1.jpg' }} style={styles.earnerAvatar} />
          <View style={styles.earnerInfo}>
            <Text style={styles.earnerName}>John Doe ✓</Text>
            <Text style={styles.earnerEarnings}>💰 1,250 TZS this week</Text>
          </View>
        </View>
        <View style={styles.earnerItem}>
          <Image source={{ uri: 'https://randomuser.me/api/portraits/women/2.jpg' }} style={styles.earnerAvatar} />
          <View style={styles.earnerInfo}>
            <Text style={styles.earnerName}>Jane Smith</Text>
            <Text style={styles.earnerEarnings}>💰 980 TZS this week</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );

  const renderChatsTab = () => {
    const renderChat = ({ item: chat }) => (
      <TouchableOpacity style={styles.chatItem} onPress={() => Alert.alert('Chat', `Opening chat with ${chat.name}`)}>
        <Image source={{ uri: chat.avatar }} style={styles.chatAvatar} />
        <View style={styles.chatInfo}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName}>{chat.name}</Text>
            <Text style={styles.chatTime}>{chat.time}</Text>
          </View>
          <Text style={styles.chatMessage}>{chat.lastMessage}</Text>
        </View>
        {chat.unread > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>{chat.unread}</Text>
          </View>
        )}
      </TouchableOpacity>
    );

    return (
      <View style={styles.tabContent}>
        <FlatList
          data={mockChats}
          renderItem={renderChat}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  };

  const renderProfileTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.profileHeader}>
        <Image source={{ uri: currentUser.avatar }} style={styles.profileAvatar} />
        <Text style={styles.profileName}>{currentUser.name} {currentUser.verified && '✓'}</Text>
        <Text style={styles.profileUsername}>@{currentUser.username}</Text>

        <View style={styles.profileStats}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{currentUser.posts}</Text>
            <Text style={styles.statLabel}>Posts</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{currentUser.followers}</Text>
            <Text style={styles.statLabel}>Followers</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{currentUser.following}</Text>
            <Text style={styles.statLabel}>Following</Text>
          </View>
        </View>
      </View>

      <View style={styles.walletSection}>
        <Text style={styles.sectionTitle}>💰 ProPay Wallet</Text>
        <View style={styles.walletCard}>
          <Text style={styles.balanceLabel}>Current Balance</Text>
          <Text style={styles.balanceAmount}>{currentUser.balance.toFixed(2)} TZS</Text>
          <View style={styles.walletActions}>
            <TouchableOpacity style={styles.walletButton} onPress={() => Alert.alert('Deposit', 'Deposit feature coming soon!')}>
              <Text style={styles.walletButtonText}>💳 Deposit</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.walletButton} onPress={() => Alert.alert('Withdraw', 'Withdraw feature coming soon!')}>
              <Text style={styles.walletButtonText}>🏦 Withdraw</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={styles.menuSection}>
        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Settings', 'Settings coming soon!')}>
          <Text style={styles.menuText}>⚙️ Settings</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Help', 'Help center coming soon!')}>
          <Text style={styles.menuText}>❓ Help & Support</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('About', 'ProChat v1.0.0\nSocial media with built-in monetization')}>
          <Text style={styles.menuText}>ℹ️ About ProChat</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#1DA1F2" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1DA1F2" />
          <Text style={styles.loadingText}>Loading ProChat...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1DA1F2" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>🚀 ProChat</Text>
        <Text style={styles.headerSubtitle}>Social Media with ProPay</Text>
      </View>

      <View style={styles.content}>
        {currentTab === 'home' && renderHomeTab()}
        {currentTab === 'discover' && renderDiscoverTab()}
        {currentTab === 'chats' && renderChatsTab()}
        {currentTab === 'profile' && renderProfileTab()}
      </View>

      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navItem, currentTab === 'home' && styles.activeNavItem]}
          onPress={() => setCurrentTab('home')}
        >
          <Text style={[styles.navText, currentTab === 'home' && styles.activeNavText]}>🏠 Home</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.navItem, currentTab === 'discover' && styles.activeNavItem]}
          onPress={() => setCurrentTab('discover')}
        >
          <Text style={[styles.navText, currentTab === 'discover' && styles.activeNavText]}>🔍 Discover</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.navItem, currentTab === 'chats' && styles.activeNavItem]}
          onPress={() => setCurrentTab('chats')}
        >
          <Text style={[styles.navText, currentTab === 'chats' && styles.activeNavText]}>💬 Chats</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.navItem, currentTab === 'profile' && styles.activeNavItem]}
          onPress={() => setCurrentTab('profile')}
        >
          <Text style={[styles.navText, currentTab === 'profile' && styles.activeNavText]}>👤 Profile</Text>
        </TouchableOpacity>
      </View>

      {currentTab === 'home' && (
        <TouchableOpacity
          style={styles.fab}
          onPress={() => setShowNewPost(true)}
        >
          <Text style={styles.fabText}>+</Text>
        </TouchableOpacity>
      )}

      <Modal visible={showNewPost} animationType="slide" transparent={true}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create New Post</Text>
            <TextInput
              style={styles.textInput}
              placeholder="What's happening?"
              multiline
              value={newPostText}
              onChangeText={setNewPostText}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={() => setShowNewPost(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.postButton} onPress={createNewPost}>
                <Text style={styles.postButtonText}>Post</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1DA1F2',
  },
  header: {
    backgroundColor: '#1DA1F2',
    padding: 15,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.8,
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 15,
  },
  listContent: {
    paddingBottom: 100,
  },
  postContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
    padding: 15,
  },
  postHeader: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
  },
  userInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  userName: {
    fontWeight: 'bold',
    fontSize: 16,
    marginRight: 5,
  },
  verified: {
    color: '#1DA1F2',
    fontSize: 16,
    marginRight: 5,
  },
  userHandle: {
    color: '#657786',
    fontSize: 14,
    marginRight: 5,
  },
  dot: {
    color: '#657786',
    marginRight: 5,
  },
  timeAgo: {
    color: '#657786',
    fontSize: 14,
  },
  postContent: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 10,
  },
  postImage: {
    width: '100%',
    height: 200,
    borderRadius: 10,
    marginBottom: 10,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    paddingVertical: 5,
  },
  statsText: {
    color: '#657786',
    fontSize: 12,
  },
  revenueText: {
    color: '#28a745',
    fontSize: 12,
    fontWeight: 'bold',
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#E1E8ED',
  },
  actionButton: {
    padding: 10,
  },
  actionText: {
    fontSize: 14,
    color: '#657786',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#1DA1F2',
  },
  trendingContainer: {
    marginBottom: 30,
  },
  trendingItem: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  trendingText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1DA1F2',
  },
  trendingCount: {
    fontSize: 14,
    color: '#657786',
    marginTop: 5,
  },
  earnersContainer: {
    marginBottom: 20,
  },
  earnerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  earnerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 15,
  },
  earnerInfo: {
    flex: 1,
  },
  earnerName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  earnerEarnings: {
    fontSize: 14,
    color: '#28a745',
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  chatAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  chatName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  chatTime: {
    fontSize: 12,
    color: '#657786',
  },
  chatMessage: {
    fontSize: 14,
    color: '#657786',
  },
  unreadBadge: {
    backgroundColor: '#1DA1F2',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginLeft: 10,
  },
  unreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  profileAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 15,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  profileUsername: {
    fontSize: 16,
    color: '#657786',
    marginBottom: 20,
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1DA1F2',
  },
  statLabel: {
    fontSize: 14,
    color: '#657786',
    marginTop: 5,
  },
  walletSection: {
    marginBottom: 30,
  },
  walletCard: {
    backgroundColor: '#f8f9fa',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 16,
    color: '#657786',
    marginBottom: 10,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#28a745',
    marginBottom: 20,
  },
  walletActions: {
    flexDirection: 'row',
    gap: 15,
  },
  walletButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
  },
  walletButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  menuSection: {
    marginBottom: 20,
  },
  menuItem: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  menuText: {
    fontSize: 16,
    color: '#333',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E1E8ED',
    paddingVertical: 10,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
  },
  activeNavItem: {
    backgroundColor: '#f0f8ff',
  },
  navText: {
    fontSize: 12,
    color: '#657786',
  },
  activeNavText: {
    color: '#1DA1F2',
    fontWeight: 'bold',
  },
  fab: {
    position: 'absolute',
    bottom: 90,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  fabText: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#1DA1F2',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E1E8ED',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    backgroundColor: '#657786',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    flex: 1,
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#fff',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  postButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    flex: 1,
    marginLeft: 10,
  },
  postButtonText: {
    color: '#fff',
    textAlign: 'center',
    fontWeight: 'bold',
  },
});