import {
    Box,
    Stat,
    StatArrow,
    StatHelpText,
    StatLabel,
    StatNumber,
    useColorModeValue
} from '@chakra-ui/react';
import React from 'react';
import { IconType } from 'react-icons';

interface StatCardProps {
  title: string;
  value: string | number;
  change: number;
  icon?: IconType;
  color?: string;
  hideChange?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, icon, color = 'blue.500', hideChange = false }) => {
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      bg={bg}
      p={6}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      boxShadow="sm"
      _hover={{ boxShadow: 'md' }}
      transition="all 0.2s"
    >
      <Stat>
        <StatLabel fontSize="sm" color="gray.500" fontWeight="medium">
          {title}
        </StatLabel>
        <StatNumber fontSize="2xl" fontWeight="bold" color={color}>
          {value}
        </StatNumber>
        {!hideChange && (
          <StatHelpText mb={0}>
            <StatArrow type={change >= 0 ? 'increase' : 'decrease'} />
            {Math.abs(change).toFixed(1)}%
          </StatHelpText>
        )}
      </Stat>
    </Box>
  );
};

export default StatCard;
